package com.edu.app.mq;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.edu.app.domain.CoursePackInfo;
import com.edu.app.dto.LearnRecordDTO;
import com.edu.app.service.ICoursePackInfoService;
import com.edu.app.service.ILearnCourseService;
import com.edu.app.service.ILearnEntityLogService;
import com.edu.common.constant.EduRedisKeys;
import com.edu.common.core.redis.RedisCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        consumerGroup = "${rocketmq.consumer.group}",
        topic = "${rocketmq.pull-consumer.topic}",
        accessKey = "${rocketmq.pull-consumer.access-key}",
        secretKey = "${rocketmq.pull-consumer.secret-key}",
        consumeMode = ConsumeMode.ORDERLY)
public class LearnRecordListener implements RocketMQListener<MessageExt> {

    @Resource
    RedisCache redisCache;
    @Resource
    Environment environment;

    private final ILearnCourseService learnCourseService;
    private final ILearnEntityLogService learnEntityLogService;
    private final ICoursePackInfoService coursePackInfoService;

//    typeId == 1 ? " 认读" : typeId == 2 ? " 辨音" : typeId == 3 ? " 拼写" : ""


    private String getEnvKey(String key) {
        String active = environment.getActiveProfiles()[0];
        if (!active.equals("dev")) {
            active = "";
        }
        return key + active;
    }

    @Override
    public void onMessage(MessageExt message) {
        if (redisCache.lock(getEnvKey(String.format(EduRedisKeys.EDU_MSG_INFO, message.getMsgId())), DateUtil.now(), 1, TimeUnit.MINUTES)) {
            try {
                String data = new String(message.getBody(), StandardCharsets.UTF_8);
                LearnRecordDTO dto = JSONObject.parseObject(data, LearnRecordDTO.class);
                log.info("收到消息:msg:{}, userId {} ", message.getMsgId(), dto.getUserId());
                if (CollectionUtils.isNotEmpty(dto.getEntity())) {
                    CoursePackInfo coursePackInfo = coursePackInfoService.getById(dto.getEntity().get(0).getPackId());
                    if (coursePackInfo.getExType() == 1) {
                        log.error("Start importing official course data, official course ID：{}", coursePackInfo.getId());
                        log.error("开始导入正式课程数据，正式课程ID：{}", coursePackInfo.getId());
                        learnCourseService.save(dto);
                    }
                }
            } catch (Exception e) {
                log.error("消息处理失败: {} {}", e.getMessage(), new String(message.getBody(), StandardCharsets.UTF_8));
                throw e;
            } finally {
                redisCache.unlock(getEnvKey(String.format(EduRedisKeys.EDU_MSG_INFO, message.getMsgId())));
            }
        }
    }


}
