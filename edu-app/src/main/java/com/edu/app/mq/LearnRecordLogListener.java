package com.edu.app.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.edu.app.domain.LearnDuration;
import com.edu.app.domain.LearnEntity;
import com.edu.app.domain.LearnEntityLog;
import com.edu.app.domain.AiReviewInfo;
import com.edu.app.domain.CoursePackInfo;
import com.edu.app.domain.WordInfo;
import com.edu.app.dto.LearnRecordDTO;
import com.edu.app.service.ILearnEntityLogService;
import com.edu.app.service.AiReviewInfoService;
import com.edu.app.service.ICoursePackInfoService;
import com.edu.app.service.IWordInfoService;
import com.edu.common.constant.Constants;
import com.edu.common.constant.EduRedisKeys;
import com.edu.common.core.redis.RedisCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        consumerGroup = "${rocketmq.consumer.learn_record_log}",
        topic = "${rocketmq.pull-consumer.topic}",
        accessKey = "${rocketmq.pull-consumer.access-key}",
        secretKey = "${rocketmq.pull-consumer.secret-key}",
        consumeMode = ConsumeMode.ORDERLY)
public class LearnRecordLogListener implements RocketMQListener<MessageExt> {

    @Resource
    RedisCache redisCache;
    @Resource
    Environment environment;

    private final ILearnEntityLogService learnEntityLogService;
    private final AiReviewInfoService aiReviewInfoService;
    private final ICoursePackInfoService coursePackInfoService;
    private final IWordInfoService wordInfoService;

    @Override
    public void onMessage(MessageExt message) {
        if (redisCache.lock(getEnvKey(String.format(EduRedisKeys.LEARN_RECORD_LOG_INFO, message.getMsgId())), DateUtil.now(), 1, TimeUnit.MINUTES)) {
            try {
                String data = new String(message.getBody(), StandardCharsets.UTF_8);
                LearnRecordDTO dto = JSONObject.parseObject(data, LearnRecordDTO.class);
                log.error("开始批量入库复习信息  before:{} {}", CollectionUtils.isNotEmpty(dto.getEntity()), dto.getDuration());
                if (CollectionUtils.isNotEmpty(dto.getEntity()) && CollectionUtils.isNotEmpty(dto.getDuration())) {
                    Integer isReview = dto.getDuration().stream().findFirst().map(LearnDuration::getIsReview).orElse(null);

                    log.error("开始批量入库复习信息 start {}", isReview);
                    // 将学习记录添加到 ai_review_info 表中
                    if (Objects.nonNull(isReview) && !isReview.equals(1)) {
                        saveAiReviewInfo(dto);
                    }
                    CoursePackInfo coursePackInfo = coursePackInfoService.getById(dto.getEntity().get(0).getPackId());
                    if (coursePackInfo.getExType().equals(1)){
                        log.error("开始导入正式课程数据，正式课程ID：{}",coursePackInfo.getId());
                        log.error("Start importing official course data, official course ID：{}",coursePackInfo.getId());
                        for (LearnEntity entity : dto.getEntity()) {
                            entity.setIsReview(isReview);
                        }
                        // 将学习记录添加到 Redis List 中
                        redisCache.lPush(getEnvKey(EduRedisKeys.LEARN_RECORD_LOG_KEY), dto.getEntity());
                    }
                }
            } catch (Exception e) {
                log.error("消息处理失败: {} {}", e.getMessage(), new String(message.getBody(), StandardCharsets.UTF_8));
                throw e;
            } finally {
                redisCache.unlock(getEnvKey(String.format(EduRedisKeys.EDU_MSG_INFO, message.getMsgId())));
            }
        }
    }

    private void saveAiReviewInfo(LearnRecordDTO dto) {
        // 创建一个空的 AiReviewInfo 列表
        List<AiReviewInfo> aiReviewInfoList = new ArrayList<>();
        // 创建一个 AiReviewInfo 对象
        AiReviewInfo aiReviewInfo;
        // 遍历 dto 中的 LearnEntity 列表
        for (LearnEntity entity : dto.getEntity()) {
            // 如果 LearnEntity 的 isError 属性为空或者等于 0，则跳过
            if (Objects.isNull(entity.getIsError()) || entity.getIsError().equals(0)) {
                continue;
            }
            // 获取课程包信息
            CoursePackInfo coursePackInfo = coursePackInfoService.getById(entity.getPackId());
            // 获取单词信息
            WordInfo wordInfo = wordInfoService.getById(entity.getEntityId());

            // 检查课程包状态和删除标记
            if (coursePackInfo != null && coursePackInfo.getStatus() == 2 && wordInfo != null) {
                aiReviewInfo = new AiReviewInfo();
                // 创建一个新的 AiReviewInfo 对象
                aiReviewInfo.setUserId(entity.getUserId());
                // 设置 AiReviewInfo 对象的属性
                aiReviewInfo.setCoursePackId(entity.getPackId());
                aiReviewInfo.setChapterId(entity.getChapterId());
                aiReviewInfo.setEduStage(coursePackInfo.getEduStageId());
                aiReviewInfo.setWordId(wordInfo.getId());
                aiReviewInfo.setWord(wordInfo.getWord());
                aiReviewInfo.setMistakeTime(entity.getCreated());
                aiReviewInfo.setReviewNum(0);
                aiReviewInfo.setStatus(0);
                aiReviewInfo.setCreateBy("system");
                aiReviewInfo.setCreateTime(new Date()); // 使用 Date 类型
                aiReviewInfoList.add(aiReviewInfo);
            }
            // 将 AiReviewInfo 对象添加到列表中
        }
        // 保存单词错误记录
        log.error("开始批量入库复习实体 save: {}", JSONObject.toJSONString(aiReviewInfoList));
        aiReviewInfoService.saveBatch(aiReviewInfoList);
    }

    private String getEnvKey(String key) {
        String active = environment.getActiveProfiles()[0];
        if (!active.equals("dev")) {
            active = "";
        }
        return key + active;
    }

    // 每10分钟执行一次批量入库操作
    @Scheduled(fixedRate = 600000) // 600000毫秒 = 10分钟
    public void batchInsert() {
        if (redisCache.lock(getEnvKey(Constants.LEARN_RECORD_LOG_LOCK_KEY), DateUtil.now(), 5, TimeUnit.MINUTES)) {
            List<Object> messageList = null;
            try {
                // 从 Redis List 中获取并清空所有消息
                messageList = redisCache.getAndClearList(getEnvKey(EduRedisKeys.LEARN_RECORD_LOG_KEY), 1000);
                if (CollectionUtils.isNotEmpty(messageList)) {
                    // 转换为 LearnEntityLog 列表
                    List<LearnEntityLog> batchList = new ArrayList<>();

                    for (Object obj : messageList) {
                        batchList.add(BeanUtil.toBean(obj, LearnEntityLog.class));
                    }

                    // 批量入库
                    learnEntityLogService.saveBatch(batchList);
                }
            } catch (Exception e) {
                log.error("消息处理失败: {}", e.getMessage());
                if (CollectionUtils.isNotEmpty(messageList)) {
                    // 将消息添加到 Redis List 中
                    redisCache.lPush(getEnvKey(EduRedisKeys.LEARN_RECORD_LOG_KEY), messageList);
                }
            } finally {
                redisCache.unlock(getEnvKey(Constants.LEARN_RECORD_LOG_LOCK_KEY));
            }
        }
    }
}
