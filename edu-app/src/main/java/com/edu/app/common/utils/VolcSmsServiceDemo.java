package com.edu.app.common.utils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 火山引擎短信服务Demo - 基于TypeScript版本实现
 * 支持注册和登录验证码发送
 * 
 * <AUTHOR>
 */
public class VolcSmsServiceDemo {
    
    private static final Logger logger = LoggerFactory.getLogger(VolcSmsServiceDemo.class);
    private static final String PRODUCT_NAME = "PleasureHub";
    
    // 火山引擎SMS配置
    private static final String SMS_ACCESS_KEY = "AKLTODI0YjdmNzc0OGI2NDNhMTk5ODIxZmFiNDZlZTNiY2M";
    private static final String SMS_SECRET_KEY = "Tldaak1HWTJZMlkxWVdJNE5HTmxNamcwTldOa01tSTVNelV4Wm1NeE1qUQ==";
    private static final String SMS_REGION = "cn-beijing";
    private static final String SMS_ACCOUNT_ID = "********";
    private static final String SMS_SIGN_NAME = "云漪互动";
    private static final String SMS_REGISTER_TEMPLATE_ID = "ST_852917d3";
    private static final String SMS_LOGIN_TEMPLATE_ID = "SPT_09a29a26";
    
    // API相关常量
    private static final String SMS_HOST = "sms.volcengineapi.com";
    private static final String SMS_SERVICE = "volcSMS";
    private static final String SMS_VERSION = "2020-01-01";
    private static final String SMS_ACTION = "SendSms";
    
    // 不参与加签的header keys
    private static final Set<String> HEADER_KEYS_TO_IGNORE = new HashSet<String>() {{
        add("authorization");
        add("content-type");
        add("content-length");
        add("user-agent");
        add("presigned-expires");
        add("expect");
    }};
    
    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * SMS配置类
     */
    public static class SMSConfig {
        private String accessKeyId;
        private String accessKeySecret;
        private String region;
        private String accountId;
        private String signName;
        private String registerTemplateId;
        private String loginTemplateId;
        
        // 构造器和getter/setter
        public SMSConfig(String accessKeyId, String accessKeySecret, String region,
                        String accountId, String signName, String registerTemplateId, String loginTemplateId) {
            this.accessKeyId = accessKeyId;
            this.accessKeySecret = accessKeySecret;
            this.region = region;
            this.accountId = accountId;
            this.signName = signName;
            this.registerTemplateId = registerTemplateId;
            this.loginTemplateId = loginTemplateId;
        }
        
        // getter方法
        public String getAccessKeyId() { return accessKeyId; }
        public String getAccessKeySecret() { return accessKeySecret; }
        public String getRegion() { return region; }
        public String getAccountId() { return accountId; }
        public String getSignName() { return signName; }
        public String getRegisterTemplateId() { return registerTemplateId; }
        public String getLoginTemplateId() { return loginTemplateId; }
    }
    
    /**
     * 短信发送结果类
     */
    public static class SMSResult {
        private boolean success;
        private String error;
        private String messageId;
        
        public SMSResult(boolean success, String error, String messageId) {
            this.success = success;
            this.error = error;
            this.messageId = messageId;
        }
        
        public boolean isSuccess() { return success; }
        public String getError() { return error; }
        public String getMessageId() { return messageId; }
        
        @Override
        public String toString() {
            return "SMSResult{success=" + success + ", error='" + error + "', messageId='" + messageId + "'}";
        }
    }
    
    /**
     * 火山引擎API请求数据类
     */
    public static class VolcengineAPIRequestData {
        @JsonProperty("SmsAccount")
        private String smsAccount;
        
        @JsonProperty("Sign")
        private String sign;
        
        @JsonProperty("TemplateID")
        private String templateId;
        
        @JsonProperty("TemplateParam")
        private String templateParam;
        
        @JsonProperty("Tag")
        private String tag;
        
        @JsonProperty("PhoneNumbers")
        private String phoneNumbers;
        
        // 构造器
        public VolcengineAPIRequestData(String smsAccount, String sign, String templateId,
                                       String templateParam, String tag, String phoneNumbers) {
            this.smsAccount = smsAccount;
            this.sign = sign;
            this.templateId = templateId;
            this.templateParam = templateParam;
            this.tag = tag;
            this.phoneNumbers = phoneNumbers;
        }
        
        // getter方法
        public String getSmsAccount() { return smsAccount; }
        public String getSign() { return sign; }
        public String getTemplateId() { return templateId; }
        public String getTemplateParam() { return templateParam; }
        public String getTag() { return tag; }
        public String getPhoneNumbers() { return phoneNumbers; }
    }
    
    /**
     * 火山引擎API响应类
     */
    public static class VolcengineAPIResponse {
        @JsonProperty("ResponseMetadata")
        private ResponseMetadata responseMetadata;
        
        @JsonProperty("Result")
        private Result result;
        
        public static class ResponseMetadata {
            @JsonProperty("RequestId")
            private String requestId;
            
            @JsonProperty("Action")
            private String action;
            
            @JsonProperty("Version")
            private String version;
            
            @JsonProperty("Service")
            private String service;
            
            @JsonProperty("Region")
            private String region;
            
            @JsonProperty("Error")
            private Error error;
            
            public static class Error {
                @JsonProperty("Code")
                private String code;
                
                @JsonProperty("Message")
                private String message;
                
                public String getCode() { return code; }
                public String getMessage() { return message; }
            }
            
            public String getRequestId() { return requestId; }
            public String getAction() { return action; }
            public String getVersion() { return version; }
            public String getService() { return service; }
            public String getRegion() { return region; }
            public Error getError() { return error; }
        }
        
        public static class Result {
            @JsonProperty("MessageID")
            private String[] messageId;
            
            public String[] getMessageId() { return messageId; }
            
            // 便捷方法：获取第一个MessageID
            public String getFirstMessageId() { 
                return messageId != null && messageId.length > 0 ? messageId[0] : null; 
            }
        }
        
        public ResponseMetadata getResponseMetadata() { return responseMetadata; }
        public Result getResult() { return result; }
    }
    
    public static void main(String[] args) {
        System.out.println("🔥 火山引擎短信服务Demo - Java版本");
        System.out.println("========================================");
        
        try {
            // 测试发送注册验证码
            testSendRegistrationCode();
            
            Thread.sleep(2000); // 等待2秒
            
            // 测试发送登录验证码
            testSendLoginCode();
            
        } catch (Exception e) {
            logger.error("测试异常", e);
            System.err.println("❌ 测试异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试发送注册验证码
     */
    private static void testSendRegistrationCode() {
        System.out.println("\n📱 测试发送注册验证码");
        System.out.println("---------------------------");
        
        String phone = "15384402485";
        String code = generateSMSVerificationCode();
        
        System.out.println("📞 手机号: " + phone);
        System.out.println("🔐 验证码: " + code);
        System.out.println("📋 类型: 注册验证码");
        System.out.println("\n⏳ 发送中...");
        
        SMSResult result = sendRegistrationCodeSMS(phone, code);
        printResult(result);
    }
    
    /**
     * 测试发送登录验证码
     */
    private static void testSendLoginCode() {
        System.out.println("\n📱 测试发送登录验证码");
        System.out.println("---------------------------");
        
        String phone = "15384402485";
        String code = generateSMSVerificationCode();
        
        System.out.println("📞 手机号: " + phone);
        System.out.println("🔐 验证码: " + code);
        System.out.println("📋 类型: 登录验证码");
        System.out.println("\n⏳ 发送中...");
        
        SMSResult result = sendLoginCodeSMS(phone, code);
        printResult(result);
    }
    
    /**
     * 生成4位数字验证码
     */
    public static String generateSMSVerificationCode() {
        return String.format("%04d", 1000 + new Random().nextInt(9000));
    }
    
    /**
     * 发送注册验证码短信
     */
    public static SMSResult sendRegistrationCodeSMS(String phone, String code) {
        try {
            SMSConfig config = new SMSConfig(
                    SMS_ACCESS_KEY, SMS_SECRET_KEY, SMS_REGION,
                    SMS_ACCOUNT_ID, SMS_SIGN_NAME, 
                    SMS_REGISTER_TEMPLATE_ID, SMS_LOGIN_TEMPLATE_ID
            );
            
            return sendSMSWithVolcengine(config, phone, code, "register");
        } catch (Exception e) {
            logger.error("发送注册验证码失败", e);
            return new SMSResult(false, e.getMessage(), null);
        }
    }
    
    /**
     * 发送登录验证码短信
     */
    public static SMSResult sendLoginCodeSMS(String phone, String code) {
        try {
            SMSConfig config = new SMSConfig(
                    SMS_ACCESS_KEY, SMS_SECRET_KEY, SMS_REGION,
                    SMS_ACCOUNT_ID, SMS_SIGN_NAME, 
                    SMS_REGISTER_TEMPLATE_ID, SMS_LOGIN_TEMPLATE_ID
            );
            
            return sendSMSWithVolcengine(config, phone, code, "login");
        } catch (Exception e) {
            logger.error("发送登录验证码失败", e);
            return new SMSResult(false, e.getMessage(), null);
        }
    }
    
    /**
     * 调用火山引擎SMS API
     */
    private static SMSResult sendSMSWithVolcengine(SMSConfig config, String phoneNumber, 
                                                  String code, String type) {
        try {
            // 根据类型选择模板ID
            String templateId = "register".equals(type) ? 
                    config.getRegisterTemplateId() : config.getLoginTemplateId();
            
            // 构建请求数据
            VolcengineAPIRequestData requestData = new VolcengineAPIRequestData(
                    config.getAccountId(),
                    config.getSignName(),
                    templateId,
                    "{\"code\":\"" + code + "\"}",
                    "register".equals(type) ? "1001" : "1002",
                    phoneNumber
            );
            
            String requestBody = objectMapper.writeValueAsString(requestData);
            logger.info("请求数据: {}", requestBody);
            
            // 获取当前时间戳
            String xDate = getDateTimeNow();
            
            // 构建签名参数
            Map<String, String> headers = new HashMap<>();
            headers.put("X-Date", xDate);
            headers.put("Host", SMS_HOST);
            
            Map<String, String> query = new HashMap<>();
            query.put("Version", SMS_VERSION);
            query.put("Action", SMS_ACTION);
            
            // 计算请求体SHA256
            String bodySha = hashSHA256(requestBody);
            
            // 计算授权签名
            String authorization = signRequest(headers, query, config.getRegion(), 
                    SMS_SERVICE, "POST", "/", config.getAccessKeyId(), 
                    config.getAccessKeySecret(), bodySha);
            
            // 构建查询字符串
            String queryString = queryParamsToString(query);
            
            // 发送HTTP请求
            RequestBody body = RequestBody.create(requestBody, MediaType.get("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url("https://" + SMS_HOST + "/?" + queryString)
                    .post(body)
                    .addHeader("X-Date", xDate)
                    .addHeader("Host", SMS_HOST)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .addHeader("Authorization", authorization)
                    .build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                logger.info("响应数据: {}", responseBody);
                
                if (!response.isSuccessful()) {
                    return new SMSResult(false, "HTTP " + response.code() + ": " + response.message(), null);
                }
                
                // 解析响应
                VolcengineAPIResponse apiResponse = objectMapper.readValue(responseBody, VolcengineAPIResponse.class);
                
                // 检查业务错误
                if (apiResponse.getResponseMetadata() != null && 
                    apiResponse.getResponseMetadata().getError() != null) {
                    String errorMsg = apiResponse.getResponseMetadata().getError().getMessage();
                    return new SMSResult(false, errorMsg != null ? errorMsg : "短信发送失败", null);
                }
                
                // 返回成功结果
                String messageId = apiResponse.getResult() != null ? 
                        apiResponse.getResult().getFirstMessageId() : null;
                return new SMSResult(true, null, messageId);
            }
            
        } catch (Exception e) {
            logger.error("发送短信异常", e);
            return new SMSResult(false, e.getMessage(), null);
        }
    }
    
    /**
     * 获取当前时间戳（ISO格式）
     */
    private static String getDateTimeNow() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(new Date());
    }
    
    /**
     * 签名请求
     */
    private static String signRequest(Map<String, String> headers, Map<String, String> query,
                                    String region, String serviceName, String method, String pathName,
                                    String accessKeyId, String secretAccessKey, String bodySha) throws Exception {
        
        String datetime = headers.get("X-Date");
        String date = datetime.substring(0, 8); // YYYYMMDD
        
        // 创建规范化请求
        String[] signHeadersResult = getSignHeaders(headers);
        String signedHeaders = signHeadersResult[0];
        String canonicalHeaders = signHeadersResult[1];
        
        String canonicalRequest = method.toUpperCase() + "\n" +
                pathName + "\n" +
                queryParamsToString(query) + "\n" +
                canonicalHeaders + "\n" + "\n" +
                signedHeaders + "\n" +
                bodySha;
        
        String credentialScope = date + "/" + region + "/" + serviceName + "/request";
        
        // 创建签名字符串
        String stringToSign = "HMAC-SHA256" + "\n" +
                datetime + "\n" +
                credentialScope + "\n" +
                hashSHA256(canonicalRequest);
        
        // 计算签名
        byte[] kDate = hmacSHA256(secretAccessKey.getBytes(StandardCharsets.UTF_8), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, serviceName);
        byte[] kSigning = hmacSHA256(kService, "request");
        String signature = bytesToHex(hmacSHA256(kSigning, stringToSign));
        
        return "HMAC-SHA256 " +
                "Credential=" + accessKeyId + "/" + credentialScope + ", " +
                "SignedHeaders=" + signedHeaders + ", " +
                "Signature=" + signature;
    }
    
    /**
     * 获取签名头部
     */
    private static String[] getSignHeaders(Map<String, String> originHeaders) {
        List<String> headerKeys = new ArrayList<>();
        
        for (String key : originHeaders.keySet()) {
            if (!HEADER_KEYS_TO_IGNORE.contains(key.toLowerCase())) {
                headerKeys.add(key.toLowerCase());
            }
        }
        
        Collections.sort(headerKeys);
        String signedHeaders = String.join(";", headerKeys);
        
        List<String> canonicalHeaderList = new ArrayList<>();
        for (String key : headerKeys) {
            String value = originHeaders.get(key);
            if (value == null) {
                // 尝试大小写不敏感查找
                for (Map.Entry<String, String> entry : originHeaders.entrySet()) {
                    if (entry.getKey().toLowerCase().equals(key)) {
                        value = entry.getValue();
                        break;
                    }
                }
            }
            canonicalHeaderList.add(key + ":" + (value != null ? value.trim() : ""));
        }
        
        String canonicalHeaders = String.join("\n", canonicalHeaderList);
        
        return new String[]{signedHeaders, canonicalHeaders};
    }
    
    /**
     * 查询参数转字符串
     */
    private static String queryParamsToString(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        
        List<String> parts = new ArrayList<>();
        for (String key : keys) {
            String value = params.get(key);
            if (value != null) {
                parts.add(uriEscape(key) + "=" + uriEscape(value));
            }
        }
        
        return String.join("&", parts);
    }
    
    /**
     * URI转义
     */
    private static String uriEscape(String str) {
        if (str == null) return "";
        
        StringBuilder result = new StringBuilder();
        for (char c : str.toCharArray()) {
            if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || 
                (c >= '0' && c <= '9') || c == '-' || c == '_' || c == '.' || c == '~') {
                result.append(c);
            } else {
                result.append('%').append(String.format("%02X", (int) c));
            }
        }
        return result.toString();
    }
    
    /**
     * SHA256哈希
     */
    private static String hashSHA256(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }
    
    /**
     * HMAC-SHA256
     */
    private static byte[] hmacSHA256(byte[] key, String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
        mac.init(secretKeySpec);
        return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * 字节数组转十六进制
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * 打印结果
     */
    private static void printResult(SMSResult result) {
        System.out.println("\n📊 发送结果:");
        System.out.println("---------------------------");
        
        if (result.isSuccess()) {
            System.out.println("✅ 状态: 发送成功");
            if (result.getMessageId() != null) {
                System.out.println("🆔 消息ID: " + result.getMessageId());
            }
            System.out.println("🎉 短信已发送，请注意查收！");
        } else {
            System.out.println("❌ 状态: 发送失败");
            System.out.println("💬 错误信息: " + result.getError());
        }
    }
} 