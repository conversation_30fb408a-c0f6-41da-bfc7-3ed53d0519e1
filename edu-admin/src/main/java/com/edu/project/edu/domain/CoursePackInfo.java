package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程包信息对象 course_pack_info
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class CoursePackInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("教育阶段ID")
    @Excel(name = "教育阶段ID")
    private Long eduStageId;

    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;

    @ApiModelProperty("封面图片")
    @Excel(name = "封面图片")
    private String coverPic;

    @ApiModelProperty("介绍简介")
    @Excel(name = "介绍简介")
    private String description;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("类别(1、单词 2、短语 3、句子)")
    @Excel(name = "类别(1、单词 2、短语 3、句子)")
    private Integer type;

    @ApiModelProperty("体验类别(1、正常 2、体验)")
    @Excel(name = "体验类别(1、正常 2、体验)")
    private Integer exType;

    @ApiModelProperty("状态是否启用(0.草稿 1.上架 2.发布 3.作废)")
    @Excel(name = "状态是否启用(0.草稿 1.上架 2.发布 3.作废)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

    @ApiModelProperty("教育阶段名称")
    @TableField(exist = false)
    private String eduStageName;

    @ApiModelProperty("课程树")
    @TableField(exist = false)
    private List<CourseInfo> courseInfoList;

}
