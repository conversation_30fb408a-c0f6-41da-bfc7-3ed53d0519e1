package com.edu.project.edu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.project.edu.domain.WordInfo;

import java.util.List;

/**
 * 字典信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface WordInfoMapper extends BaseMapper<WordInfo> {
    /**
     * 查询字典信息
     *
     * @param id 字典信息ID
     * @return 字典信息
     */
    WordInfo selectWordInfoById(Long id);

    /**
     * 查询字典信息列表
     *
     * @param wordInfo 字典信息
     * @return 字典信息集合
     */
    List<WordInfo> selectWordInfoList(WordInfo wordInfo);

    /**
     * 新增字典信息
     *
     * @param wordInfo 字典信息
     * @return 结果
     */
    int insertWordInfo(WordInfo wordInfo);

    /**
     * 修改字典信息
     *
     * @param wordInfo 字典信息
     * @return 结果
     */
    int updateWordInfo(WordInfo wordInfo);

    /**
     * 删除字典信息
     *
     * @param id 字典信息ID
     * @return 结果
     */
    int deleteWordInfoById(Long id);

    /**
     * 批量删除字典信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWordInfoByIds(Long[] ids);
}
