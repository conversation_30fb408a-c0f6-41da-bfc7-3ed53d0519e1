package com.edu.project.edu.common.manager;

import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.edu.common.core.redis.RedisCache;
import com.edu.common.enums.CoursePackStatus;
import com.edu.common.task.Task;
import com.edu.common.task.TaskService;
import com.edu.common.utils.StringUtils;
import com.edu.common.utils.sql.SqliteUtils;
import com.edu.framework.storage.StorageService;
import com.edu.project.edu.domain.*;
import com.edu.project.edu.service.*;
import com.edu.system.domain.SysStorage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.Md5Crypt;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.edu.common.utils.sql.SqliteUtils.FILE_JDBC_URL;

@Slf4j
@Component
@RequiredArgsConstructor
public class CoursePackManager {

    private final ICoursePackInfoService coursePackInfoService;
    private final ICourseInfoService courseInfoService;
    private final ICourseWordService courseWordService;
    private final IWordInfoService wordInfoService;
    private final IWordTestInfoService wordTestInfoService;
    private final StorageService storageService;
    private final TaskService taskService;
    private final RedisCache redisCache;

    private static final String COURSE_PACK_STATUS_KEY = "course_pack_status_";
    private static final String COURSE_STATUS_KEY = "course_status_";

    String CHAPTER_TABLE = "chapter";
    String[] CHAPTER_KEYS = {"id", "course_id", "chapter_id", "chapter_title", "lesson", "lesson_id", "chapter_sort", "word_id", "word", "phonetic_uk", "phonetic_us", "photo", "audio", "difficulty", "basic_explaination", "all_explaination", "phrase", "example", "reference", "word_sort", "version"};
    String CHAPTER_DDL = "CREATE TABLE [chapter]( [id] INTEGER, [course_id] INT(11), [chapter_id] INT(11), [chapter_title] TEXT, [lesson] TEXT, [lesson_id] INT(11), [chapter_sort] INT(11), [word_id] INT(11), [word] TEXT, [phonetic_uk] TEXT, [phonetic_us] TEXT, [photo] TEXT, [audio] TEXT, [difficulty] TEXT, [basic_explaination] TEXT, [all_explaination] TEXT, [phrase] TEXT, [example] TEXT, [reference] TEXT, [word_sort] INT(11), [version] INT(11));";
    String RES_TABLE = "res";
    String[] RES_KEYS = {"id", "word_id", "word", "photo", "audio_us", "audio_uk", "version"};
    String RES_DDL = "CREATE TABLE [res]( [id] INTEGER, [word_id] INT(11), [word] TEXT, [photo] TEXT, [audio_us] BLOB, [audio_uk] BLOB, [version] INT(11));";
    String EXAM_TABLE = "exam";
    String[] EXAM_KEYS = {"id", "word_id", "word", "correct", "error1", "error2", "error3", "type"};
    String EXAM_DDL = "CREATE TABLE [exam]( [id] INTEGER, [word_id] INT(11), [word] TEXT, [correct] TEXT, [error1] TEXT, [error2] TEXT, [error3] TEXT, [type] INT(11));";
    Map<String, String[]> TABLES = new HashMap<String, String[]>() {{
        put(CHAPTER_TABLE, CHAPTER_KEYS);
        put(EXAM_TABLE, EXAM_KEYS);
        put(RES_TABLE, RES_KEYS);
    }};
    Map<String, String> TABLE_DDL = new HashMap<String, String>() {{
        put(CHAPTER_TABLE, CHAPTER_DDL);
        put(EXAM_TABLE, EXAM_DDL);
        put(RES_TABLE, RES_DDL);
    }};

    public enum Status {
        INIT(0),
        PROCESSING(1),
        FINISH(2),
        ERROR(3);
        private final int value;

        Status(int value) {
            this.value = value;
        }
    }


    /**
     * 创建db文件
     */
    public void createCourseDB(Long packId) {
        // 获取课程包信息
        final CoursePackInfo packInfo = coursePackInfoService.getById(packId);
        //上架的时候生成
        if (!Objects.equals(packInfo.getStatus(), CoursePackStatus.PUT_AWAY.getCode())) {
            return;
        }

        //更新课程包状态 初始化
        updatePackStatus(packId, Status.PROCESSING);
        // 获取课程信息
        List<CourseInfo> courses = courseInfoService.lambdaQuery().eq(CourseInfo::getCoursePackId, packId).list();
        // 遍历课程信息
        List<CourseInfo> courseList = courses.stream().filter(courseInfo -> courseInfo.getLevel() == 1).collect(Collectors.toList());
        CountDownLatch downLatch = new CountDownLatch(courseList.size());
        courseList.forEach(courseInfo -> {
            createCoursePackTask(downLatch, packInfo, courseInfo, courses);
        });
        downLatch.countDown();
        //更新课程状态 处理完成
        updatePackStatus(packId, Status.FINISH);

    }

    /**
     * 创建课程包任务
     *
     * @param downLatch
     * @param packInfo
     * @param courseInfo
     * @param courses
     */
    private void createCoursePackTask(CountDownLatch downLatch, CoursePackInfo packInfo, CourseInfo courseInfo, List<CourseInfo> courses) {
        //更新课程状态 处理中
        updateCourseStatus(courseInfo.getId(), Status.INIT);
        taskService.addTask(new Task(String.valueOf(courseInfo.getId()), 0L, true) {
            @Override
            public void run() {
                try {
                    updateCourseStatus(courseInfo.getId(), Status.PROCESSING);
                    SysStorage storage = createCoursePack(packInfo, courseInfo, courses);
                    String oldUrl = courseInfo.getDownloadUrl();
                    courseInfo.setDownloadUrl(storage.getUrl());
                    courseInfo.setFileSize(storage.getSize());
                    courseInfo.setChecksum(storage.getMd5());
                    courseInfoService.updateCourseInfoDownloadUrl(courseInfo);
                    if (StringUtils.isNotEmpty(oldUrl)) {
                        storageService.delete(URLUtil.toURI(oldUrl).getPath());
                    }
                    //更新课程状态 处理成功
                    updateCourseStatus(courseInfo.getId(), Status.FINISH);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("生成课程包失败 {}", e.getMessage());
                    //更新课程状态 处理失败
                    updateCourseStatus(courseInfo.getId(), Status.ERROR);
                } finally {
                    try {
                        downLatch.await(5, TimeUnit.MINUTES);
                    } catch (InterruptedException e) {
                    }
                }

            }
        });
    }

    /**
     * 处理更新课程包状态
     *
     * @param packId
     * @param status
     */
    private void updatePackStatus(Long packId, Status status) {
        redisCache.setCacheObject(COURSE_PACK_STATUS_KEY + packId, status.value, 60 * 60 * 2, TimeUnit.SECONDS);
    }

    /**
     * 处理更新课程状态
     *
     * @param courseId
     * @param status
     */
    private void updateCourseStatus(Long courseId, Status status) {
        redisCache.setCacheObject(COURSE_STATUS_KEY + courseId, status.value, 60 * 60 * 2, TimeUnit.SECONDS);
    }


    /**
     * 创建课程包
     *
     * @param packInfo
     * @param courseInfo
     * @param courses
     * @return
     */
    private SysStorage createCoursePack(CoursePackInfo packInfo, CourseInfo courseInfo, List<CourseInfo> courses) {
        List<Map<String, Object>> chapterData = new ArrayList<>();
        List<Map<String, Object>> resData = new ArrayList<>();
        List<Map<String, Object>> examData = new ArrayList<>();
        // 获取章节信息
        courses.stream().filter(v -> Objects.equals(courseInfo.getId(), v.getTopId())).filter(v -> v.getLevel() == 2).forEach(chapterInfo -> {
            // 获取课时信息
            courses.stream().filter(v -> Objects.equals(chapterInfo.getId(), v.getTopId())).filter(v -> v.getLevel() == 3).forEach(lessonInfo -> {
                // 获取单词ID列表
                List<Long> words = courseWordService.lambdaQuery().eq(CourseWord::getCoursePackId, packInfo.getId()).eq(CourseWord::getCourseId, lessonInfo.getId()).list().stream().map(CourseWord::getWordId).collect(Collectors.toList());
                if (!words.isEmpty()) {
                    // 获取单词测试信息
                    Map<String, WordTestInfo> testMap = wordTestInfoService.lambdaQuery().in(WordTestInfo::getWordId, words).list().stream().collect(Collectors.toMap(WordTestInfo::getTestKey, Function.identity()));
                    // 遍历单词信息
                    for (WordInfo wordInfo : wordInfoService.lambdaQuery().in(WordInfo::getId, words).list()) {
                        // 获取章节单词信息
                        Map<String, Object> chapterMap = getChapterWord(courseInfo, chapterInfo, lessonInfo, wordInfo);
                        chapterData.add(chapterMap);
                        // 获取单词资源信息
                        Map<String, Object> resMap = getResWord(wordInfo);
                        resData.add(resMap);
                        // 获取考试单词信息
                        Map<String, Object> examMapType1 = getExamWord(wordInfo, testMap.get(wordInfo.getId().toString() + "-1"));
                        Map<String, Object> examMapType2 = getExamWord(wordInfo, testMap.get(wordInfo.getId().toString() + "-2"));
                        if (Objects.nonNull(examMapType1)) {
                            examData.add(examMapType1);
                        }
                        if (Objects.nonNull(examMapType2)) {
                            examData.add(examMapType2);
                        }
                    }
                }
            });
        });
        return createCourseDB(packInfo, courseInfo, chapterData, resData, examData);
    }


    /**
     * 创建课程db 文件
     *
     * @param packInfo
     * @param courseInfo
     * @param chapterData
     * @param resData
     * @param examData
     * @return
     */
    private SysStorage createCourseDB(CoursePackInfo packInfo, CourseInfo courseInfo, List<Map<String, Object>> chapterData, List<Map<String, Object>> resData, List<Map<String, Object>> examData) {
        //创建数据库文件
        String fileName = packInfo.getId() + "/" + courseInfo.getId() + "/course_data.db";
        String filePath = createCourseDBFile(fileName);
        try {
            String dbUrl = FILE_JDBC_URL + filePath;
            //创建表结构
            for (Map.Entry<String, String> table : TABLE_DDL.entrySet()) {
                SqliteUtils.createTable(dbUrl, table.getKey(), table.getValue());
            }
            Map<String, List<Map<String, Object>>> tableData = new HashMap<String, List<Map<String, Object>>>() {{
                put(CHAPTER_TABLE, chapterData);
                put(EXAM_TABLE, examData);
                put(RES_TABLE, resData);
            }};

            //插入数据
            for (Map.Entry<String, List<Map<String, Object>>> entry : tableData.entrySet()) {
                SqliteUtils.write(dbUrl, entry.getKey(), entry.getValue());
            }
            //上传文件
            return uploadDBFile(filePath, fileName);
        } catch (Exception e) {
            log.error("创建课程db 文件失败", e);
        } finally {
            //删除临时文件
            FileUtils.deleteQuietly(new File(filePath));
        }
        return null;

    }

    private String createCourseDBFile(String fileName) {
        try {
            File file = File.createTempFile(Md5Crypt.md5Crypt(fileName.getBytes(StandardCharsets.UTF_8)), FilenameUtils.getExtension(fileName));
            return file.getPath();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private SysStorage uploadDBFile(String filePath, String fileName) throws Exception {
        File file = new File(filePath);
        return storageService.store(Files.newInputStream(Paths.get(filePath)), FileUtils.sizeOf(file), MediaType.APPLICATION_OCTET_STREAM_VALUE, "db/" + fileName);
    }


    /**
     * 获取考试单词
     *
     * @param wordInfo     单词信息
     * @param wordTestInfo 单词测试信息
     * @return
     */
    private Map<String, Object> getExamWord(WordInfo wordInfo, WordTestInfo wordTestInfo) {
        if (Objects.isNull(wordTestInfo)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put(EXAM_KEYS[0], wordTestInfo.getId());
        map.put(EXAM_KEYS[1], wordInfo.getId());
        map.put(EXAM_KEYS[2], wordInfo.getWord());
        map.put(EXAM_KEYS[3], wordTestInfo.getCorrect());
        map.put(EXAM_KEYS[4], wordTestInfo.getError1());
        map.put(EXAM_KEYS[5], wordTestInfo.getError2());
        map.put(EXAM_KEYS[6], wordTestInfo.getError3());
        map.put(EXAM_KEYS[7], wordTestInfo.getType());
        return map;
    }

    /**
     * 获取单词资源
     *
     * @param wordInfo 单词信息
     * @return
     */
    private Map<String, Object> getResWord(WordInfo wordInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put(RES_KEYS[0], wordInfo.getId());
        map.put(RES_KEYS[1], wordInfo.getId());
        map.put(RES_KEYS[2], wordInfo.getWord());
        map.put(RES_KEYS[3], wordInfo.getPhotoUrl());
//        map.put(RES_KEYS[3], null);
        map.put(RES_KEYS[4], downloadRes(wordInfo.getAudioUs()));
        map.put(RES_KEYS[5], downloadRes(wordInfo.getAudioUk()));
        map.put(RES_KEYS[6], wordInfo.getVersion());
        return map;
    }

    /**
     * 下载资源
     *
     * @param url
     * @return
     */
    private byte[] downloadRes(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            HttpUtil.download(url, out, false);
            return out.toByteArray();
        } catch (Exception e) {
            log.error("下载资源失败 url:{}  msg:{}", url, e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取章节单词
     *
     * @param courseInfo  课程信息
     * @param chapterInfo 章节信息
     * @param lessonInfo  课时信息
     * @param wordInfo    单词信息
     * @return
     */
    private Map<String, Object> getChapterWord(CourseInfo courseInfo, CourseInfo chapterInfo, CourseInfo lessonInfo, WordInfo wordInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put(CHAPTER_KEYS[0], wordInfo.getId());
        map.put(CHAPTER_KEYS[1], courseInfo.getId());
        map.put(CHAPTER_KEYS[2], chapterInfo.getId());
        map.put(CHAPTER_KEYS[3], chapterInfo.getName());
        map.put(CHAPTER_KEYS[4], lessonInfo.getName());
        map.put(CHAPTER_KEYS[5], lessonInfo.getId());
        map.put(CHAPTER_KEYS[6], lessonInfo.getSort());
        map.put(CHAPTER_KEYS[7], wordInfo.getId());
        map.put(CHAPTER_KEYS[8], wordInfo.getWord());
        map.put(CHAPTER_KEYS[9], wordInfo.getPhoneticUk());
        map.put(CHAPTER_KEYS[10], wordInfo.getPhoneticUs());
        map.put(CHAPTER_KEYS[11], wordInfo.getPhoto());
        map.put(CHAPTER_KEYS[12], Optional.ofNullable(wordInfo.getAudioUk()).map(FilenameUtils::getName).orElse(Optional.ofNullable(wordInfo.getAudioUs()).map(FilenameUtils::getName).orElse(null)));
        map.put(CHAPTER_KEYS[13], wordInfo.getDifficulty());
        map.put(CHAPTER_KEYS[14], wordInfo.getBasicExplain());
        map.put(CHAPTER_KEYS[15], wordInfo.getAllExplain());
        map.put(CHAPTER_KEYS[16], wordInfo.getPhrase());
        map.put(CHAPTER_KEYS[17], wordInfo.getExample());
        map.put(CHAPTER_KEYS[18], wordInfo.getReference());
        map.put(CHAPTER_KEYS[19], wordInfo.getSort());
        map.put(CHAPTER_KEYS[19], wordInfo.getVersion());
        return map;
    }

}
