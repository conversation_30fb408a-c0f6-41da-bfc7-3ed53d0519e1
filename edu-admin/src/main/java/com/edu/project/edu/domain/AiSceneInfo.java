package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * AI数字人场景信息对象 ai_scene_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class AiSceneInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 场景分类ID
     */
    @Excel(name = "场景分类ID")
    private Long sceneTypeId;

    /**
     * 场景分类名称
     */
    @Excel(name = "场景分类名称")
    private String sceneTypeName;

    /**
     * 类别(0.无配置 1.有配置类别)
     */
    @Excel(name = "类别(0.无配置 1.有配置类别)")
    private String type;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 封面图片
     */
    @Excel(name = "封面图片")
    private String cover;

    /**
     * 模型
     */
    @Excel(name = "模型")
    private String modelUrl;

    /**
     * 排序值
     */
    @Excel(name = "排序值")
    private Integer sort;

    /**
     * 其他配置json
     */
    @Excel(name = "其他配置json")
    private String otherJson;

    /**
     * 其他信息
     */
    @Excel(name = "其他信息")
    private String otherInfo;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * tenant_id
     */
    @Excel(name = "tenant_id")
    private Long tenantId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

}
