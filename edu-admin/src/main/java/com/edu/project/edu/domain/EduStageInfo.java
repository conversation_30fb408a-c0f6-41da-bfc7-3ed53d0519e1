package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 教育阶段信息对象 edu_stage_info
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class EduStageInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("上级ID(顶级[阶段]为null)")
    @Excel(name = "上级ID(顶级[阶段]为null)")
    private Long topId;

    @ApiModelProperty("层级(1、阶段 2、级别)")
    @Excel(name = "层级(1、阶段 2、级别)")
    private Integer level;

    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("是否启用(0.是 1.否)")
    @Excel(name = "是否启用(0.是 1.否)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;


    @ApiModelProperty("年级子集")
    @TableField(exist = false)
    private List<EduStageInfo> children;
}
