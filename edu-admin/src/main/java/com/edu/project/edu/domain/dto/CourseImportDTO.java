package com.edu.project.edu.domain.dto;

import com.edu.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CourseImportDTO implements Serializable {

    @ApiModelProperty("ID")
    @Excel(name = "id")
    private Long id;

    @ApiModelProperty("课时ID")
    @Excel(name = "course_id")
    private Long courseId;

    @ApiModelProperty("章节ID")
    @Excel(name = "chapter_id")
    private Long chapterId;

    @ApiModelProperty("章节名称")
    @Excel(name = "chapter_title")
    private String chapterTitle;

    @ApiModelProperty("课时名称")
    @Excel(name = "lesson")
    private String lesson;

    @ApiModelProperty("课时ID")
    @Excel(name = "lesson_id")
    private Long lessonId;

    @ApiModelProperty("章节排序")
    @Excel(name = "chapter_sort")
    private Integer chapterSort;

    @ApiModelProperty("单词ID")
    @Excel(name = "word_id")
    private Long wordId;

    @ApiModelProperty("单词")
    @Excel(name = "word")
    private String word;

    @ApiModelProperty("英式发音解释")
    @Excel(name = "phonetic_uk")
    private String phoneticUk;

    @ApiModelProperty("美式发音解释")
    @Excel(name = "phonetic_us")
    private String phoneticUs;

    @ApiModelProperty("图片")
    @Excel(name = "photo")
    private String photo;

    @ApiModelProperty("语音")
    @Excel(name = "audio")
    private String audio;

    @ApiModelProperty("难点")
    @Excel(name = "difficulty")
    private String difficulty;

    @ApiModelProperty("基础翻译")
    @Excel(name = "basic_explaination")
    private String basicExplaination;

    @ApiModelProperty("所有翻译")
    @Excel(name = "all_explaination")
    private String allExplaination;

    @ApiModelProperty("短语")
    @Excel(name = "phrase")
    private String phrase;

    @ApiModelProperty("样例")
    @Excel(name = "example")
    private String example;

    @ApiModelProperty("参考")
    @Excel(name = "reference")
    private String reference;

    @ApiModelProperty("单词排序")
    @Excel(name = "word_sort")
    private String wordSort;

    @ApiModelProperty("版本")
    @Excel(name = "version")
    private String version;
}
