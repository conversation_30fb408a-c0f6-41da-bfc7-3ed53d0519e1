package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 考试记录对象 exam_record
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public class ExamRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 学习考试ID
     */
    @Excel(name = "学习考试ID")
    private Long learnExamId;

    /**
     * 答案状态
     */
    @Excel(name = "答案状态")
    private Long answerStatus;

    /**
     * 答题时长
     */
    @Excel(name = "答题时长")
    private Long duration;

    /**
     * 问题
     */
    @Excel(name = "问题")
    private String question;

    /**
     * 问题ID
     */
    @Excel(name = "问题ID")
    private Long questionId;

    /**
     * 问题类型
     */
    @Excel(name = "问题类型")
    private Long questionType;

    /**
     * 用户答案
     */
    @Excel(name = "用户答案")
    private String userAnswer;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setLearnExamId(Long learnExamId) {
        this.learnExamId = learnExamId;
    }

    public Long getLearnExamId() {
        return learnExamId;
    }

    public void setAnswerStatus(Long answerStatus) {
        this.answerStatus = answerStatus;
    }

    public Long getAnswerStatus() {
        return answerStatus;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getDuration() {
        return duration;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionType(Long questionType) {
        this.questionType = questionType;
    }

    public Long getQuestionType() {
        return questionType;
    }

    public void setUserAnswer(String userAnswer) {
        this.userAnswer = userAnswer;
    }

    public String getUserAnswer() {
        return userAnswer;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("learnExamId", getLearnExamId())
                .append("answerStatus", getAnswerStatus())
                .append("duration", getDuration())
                .append("question", getQuestion())
                .append("questionId", getQuestionId())
                .append("questionType", getQuestionType())
                .append("userAnswer", getUserAnswer())
                .toString();
    }
}
