package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.AiDialogueInfo;
import com.edu.project.edu.service.IAiDialogueInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI数字人课文对话Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/ai/dialogue")
@Api(tags = " AI数字人课文语句API")
public class AiDialogueInfoController extends BaseController {
    @Autowired
    private IAiDialogueInfoService aiDialogueInfoService;

//    /**
//     * 查询AI数字人课文对话列表
//     */
//    @ApiOperation("查询AI数字人课文对话列表")
//    @GetMapping("/list")
//    public TableDataInfo list(AiDialogueInfo aiDialogueInfo) {
//        startPage();
//        List<AiDialogueInfo> list = aiDialogueInfoService.selectAiDialogueInfoList(aiDialogueInfo);
//        return getDataTable(list);
//    }
//
//    /**
//     * 获取AI数字人课文对话详细信息
//     */
//    @ApiOperation("获取AI数字人课文对话详细信息")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return AjaxResult.success(aiDialogueInfoService.selectAiDialogueInfoById(id));
//    }
//
//    /**
//     * 新增AI数字人课文对话
//     */
//    @ApiOperation("新增AI数字人课文对话")
//    @Log(title = "AI数字人课文对话", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody AiDialogueInfo aiDialogueInfo) {
//        return toAjax(aiDialogueInfoService.insertAiDialogueInfo(aiDialogueInfo));
//    }
//
//    /**
//     * 修改AI数字人课文对话
//     */
//    @ApiOperation("修改AI数字人课文对话")
//    @Log(title = "AI数字人课文对话", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody AiDialogueInfo aiDialogueInfo) {
//        return toAjax(aiDialogueInfoService.updateAiDialogueInfo(aiDialogueInfo));
//    }
//
//    /**
//     * 删除AI数字人课文对话
//     */
//    @ApiOperation("删除AI数字人课文对话")
//    @Log(title = "AI数字人课文对话", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(aiDialogueInfoService.deleteAiDialogueInfoByIds(ids));
//    }
}
