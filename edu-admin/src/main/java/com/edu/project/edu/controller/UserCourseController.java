package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.UserCourse;
import com.edu.project.edu.domain.dto.AddUserCourseDTO;
import com.edu.project.edu.service.IUserCourseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户课程包关联Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/userCourse")
@Api(tags = "用户课程包API")
public class UserCourseController extends BaseController {
    @Resource
    private IUserCourseService userCourseService;

    /**
     * 查询用户课程包关联列表
     */
//    @PreAuthorize("@ss.hasPermi('userCourse:course:list')")
    @ApiOperation("查询用户课程包关联列表")
    @GetMapping("/list")
    public TableDataInfo list(UserCourse userCourse) {
        startPage();
        List<UserCourse> list = userCourseService.selectUserCourseList(userCourse);
        return getDataTable(list);
    }

    /**
     * 新增用户课程包关联
     */
//    @PreAuthorize("@ss.hasPermi('userCourse:course:add')")
    @Log(title = "用户课程包关联", businessType = BusinessType.INSERT)
    @ApiOperation("新增用户课程包关联")
    @PostMapping("add")
    public AjaxResult add(@RequestBody UserCourse userCourse) {
        return AjaxResult.success(userCourseService.insertUserCourse(userCourse));
    }

    /**
     * 新增用户课程包关联
     */
//    @PreAuthorize("@ss.hasPermi('userCourse:course:add')")
    @Log(title = "用户课程包关联多个", businessType = BusinessType.INSERT)
    @ApiOperation("新增用户课程包关联多个")
    @PostMapping("addList")
    public AjaxResult addList(@RequestBody List<UserCourse> userCourses) {
        return userCourseService.addList(userCourses);
    }

    /**
     * 多用户关联多课程
     */
    @PreAuthorize("@ss.hasPermi('userCourse:course:add')")
    @Log(title = "用户课程包关联多个", businessType = BusinessType.INSERT)
    @ApiOperation("多用户关联多课程")
    @PostMapping("addCourseList")
    public AjaxResult addCourseList(@RequestBody AddUserCourseDTO userCourseDTO) {
        return userCourseService.addCourseList(userCourseDTO);
    }

    /**
     * 删除用户课程包关联
     */
    @PreAuthorize("@ss.hasPermi('userCourse:course:remove')")
    @Log(title = "用户课程包关联", businessType = BusinessType.DELETE)
    @ApiOperation("删除用户课程包关联")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return AjaxResult.success(userCourseService.deleteUserCourseById(id));
    }
}
