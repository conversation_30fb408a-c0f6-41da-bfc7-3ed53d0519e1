package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * AI数字人老师信息对象 ai_teacher_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class AiTeacherInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String headPic;

    /**
     * 模型
     */
    @Excel(name = "模型")
    private String modelUrl;

    /**
     * 类型(0.系统 1.用户自定义)
     */
    @Excel(name = "类型(0.系统 1.用户自定义)")
    private String type;

    /**
     * 自定义用户id
     */
    @Excel(name = "自定义用户id")
    private Long userId;

    /**
     * 其他信息
     */
    @Excel(name = "其他信息")
    private String otherInfo;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * tenant_id
     */
    @Excel(name = "tenant_id")
    private Long tenantId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

}
