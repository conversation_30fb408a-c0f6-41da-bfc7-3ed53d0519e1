package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户课程包关联对象 user_course
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class UserCourse extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    @ApiModelProperty("课程包ID")
    @Excel(name = "课程包ID")
    private Long coursePackId;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("状态是否启用(0.上线 1.下线)")
    @Excel(name = "状态是否启用(0.上线 1.下线)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

    @ApiModelProperty("课程包")
    @TableField(exist = false)
    private CoursePackInfo coursePackInfo;

}
