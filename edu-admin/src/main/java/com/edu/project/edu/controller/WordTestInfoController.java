package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.WordTestInfo;
import com.edu.project.edu.service.IWordTestInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 字典评测信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/wordTest")
@Api(tags = "字典评测API")
public class WordTestInfoController extends BaseController {
    @Resource
    private IWordTestInfoService wordTestInfoService;


    /**
     * 新增字典评测信息
     */
    @PreAuthorize("@ss.hasPermi('wordTest:info:add')")
    @Log(title = "字典评测信息", businessType = BusinessType.INSERT)
    @ApiOperation("新增字典评测信息")
    @PostMapping
    public AjaxResult add(@RequestBody WordTestInfo wordTestInfo) {
        return AjaxResult.success(wordTestInfoService.insertWordTestInfo(wordTestInfo));
    }

    /**
     * 修改字典评测信息
     */
    @PreAuthorize("@ss.hasPermi('wordTest:info:edit')")
    @Log(title = "字典评测信息", businessType = BusinessType.UPDATE)
    @ApiOperation("修改字典评测信息")
    @PutMapping
    public AjaxResult edit(@RequestBody WordTestInfo wordTestInfo) {
        return AjaxResult.success(wordTestInfoService.updateWordTestInfo(wordTestInfo));
    }

    /**
     * 删除字典评测信息
     */
    @PreAuthorize("@ss.hasPermi('wordTest:info:remove')")
    @Log(title = "字典评测信息", businessType = BusinessType.DELETE)
    @ApiOperation("批量删除字典评测信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wordTestInfoService.deleteWordTestInfoByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('wordTest:info:query')")
    @ApiOperation("获取单词评测信息")
    @GetMapping(value = "/get")
    public AjaxResult get(@RequestParam("id") @ApiParam("试题ID") Long id) {
        return wordTestInfoService.get(id);
    }


    @PreAuthorize("@ss.hasPermi('wordTest:info:query')")
    @ApiOperation("获取单个单词评测信息")
    @GetMapping(value = "/getWordTest")
    public AjaxResult getWordTest(@RequestParam("wordId") @ApiParam("单词ID") Long wordId) {
        return wordTestInfoService.getWordTest(wordId);
    }

    @PreAuthorize("@ss.hasPermi('wordTest:info:query')")
    @ApiOperation("获取课时所有评测信息")
    @GetMapping(value = "/getCourseTest")
    public AjaxResult getCourseTest(@RequestParam("courseId") @ApiParam("课时ID") Long courseId) {
        return wordTestInfoService.getCourseTest(courseId);
    }

    @PreAuthorize("@ss.hasPermi('wordTest:info:list')")
    @Log(title = "导入字典测试题", businessType = BusinessType.IMPORT)
    @ApiOperation("导入字典测试题")
    @PostMapping("/importWordTest")
    public AjaxResult importWordTest(MultipartFile file, @RequestParam(value = "courseId", required = false) @ApiParam("课程ID") Long courseId) {
        if (Objects.isNull(file) || file.isEmpty()) {
            return AjaxResult.error("Import file not exists!");
        }
        try {
            return wordTestInfoService.importWordTest(file, courseId);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导入异常");
        }
    }
}
