package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.EduStageInfo;
import com.edu.project.edu.service.IEduStageInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 教育阶段信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/stage")
@Api(tags = "教育阶段API")
public class EduStageInfoController extends BaseController {
    @Resource
    private IEduStageInfoService eduStageInfoService;

    /**
     * 查询教育阶段信息列表
     */
//    @PreAuthorize("@ss.hasPermi('stage:info:list')")
    @GetMapping("/list")
    @ApiOperation("获取教育阶段列表信息(包含子集不分页)")
    public AjaxResult list(EduStageInfo eduStageInfo) {
        List<EduStageInfo> list = eduStageInfoService.selectEduStageInfoList(eduStageInfo);
        return AjaxResult.success(list);
    }

    /**
     * 获取教育阶段信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('stage:info:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取教育阶段详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(eduStageInfoService.selectEduStageInfoById(id));
    }

    /**
     * 新增教育阶段信息
     */
    @PreAuthorize("@ss.hasPermi('stage:info:add')")
    @Log(title = "教育阶段信息", businessType = BusinessType.INSERT)
    @ApiOperation("新增教育阶段")
    @PostMapping
    public AjaxResult add(@RequestBody EduStageInfo eduStageInfo) {
        return AjaxResult.success(eduStageInfoService.insertEduStageInfo(eduStageInfo));
    }

    /**
     * 修改教育阶段信息
     */
    @PreAuthorize("@ss.hasPermi('stage:info:edit')")
    @Log(title = "教育阶段信息", businessType = BusinessType.UPDATE)
    @ApiOperation("修改教育阶段")
    @PutMapping
    public AjaxResult edit(@RequestBody EduStageInfo eduStageInfo) {
        return AjaxResult.success(eduStageInfoService.updateEduStageInfo(eduStageInfo));
    }

    /**
     * 删除教育阶段信息
     */
    @PreAuthorize("@ss.hasPermi('stage:info:remove')")
    @Log(title = "教育阶段信息", businessType = BusinessType.DELETE)
    @ApiOperation("删除教育阶段")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return AjaxResult.success(eduStageInfoService.deleteEduStageInfoById(id));
    }
}
