package com.edu.project.edu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.project.edu.domain.UserCourse;

import java.util.List;

/**
 * 用户课程包关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface UserCourseMapper extends BaseMapper<UserCourse> {
    /**
     * 查询用户课程包关联
     *
     * @param id 用户课程包关联ID
     * @return 用户课程包关联
     */
    UserCourse selectUserCourseById(Long id);

    /**
     * 查询用户课程包关联列表
     *
     * @param userCourse 用户课程包关联
     * @return 用户课程包关联集合
     */
    List<UserCourse> selectUserCourseList(UserCourse userCourse);

    /**
     * 新增用户课程包关联
     *
     * @param userCourse 用户课程包关联
     * @return 结果
     */
    int insertUserCourse(UserCourse userCourse);

    /**
     * 修改用户课程包关联
     *
     * @param userCourse 用户课程包关联
     * @return 结果
     */
    int updateUserCourse(UserCourse userCourse);

    /**
     * 删除用户课程包关联
     *
     * @param id 用户课程包关联ID
     * @return 结果
     */
    int deleteUserCourseById(Long id);

    /**
     * 批量删除用户课程包关联
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteUserCourseByIds(Long[] ids);
}
