package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 学习进度对象 learn_schedule
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public class LearnSchedule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 课程ID
     */
    @Excel(name = "课程ID")
    private Long courseId;

    /**
     * 课时ID
     */
    @Excel(name = "课时ID")
    private Long lessonId;

    /**
     * 包ID
     */
    @Excel(name = "包ID")
    private Long packId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "2024-01-01 12:12:12")
    private Date created;

    /**
     * 练习进度
     */
    @Excel(name = "练习进度")
    private Long exerciseSchedule;

    /**
     * 学习进度
     */
    @Excel(name = "学习进度")
    private Long schedule;

    /**
     * 总进度
     */
    @Excel(name = "总进度")
    private Long totalSchedule;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    public Long getPackId() {
        return packId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getCreated() {
        return created;
    }

    public void setExerciseSchedule(Long exerciseSchedule) {
        this.exerciseSchedule = exerciseSchedule;
    }

    public Long getExerciseSchedule() {
        return exerciseSchedule;
    }

    public void setSchedule(Long schedule) {
        this.schedule = schedule;
    }

    public Long getSchedule() {
        return schedule;
    }

    public void setTotalSchedule(Long totalSchedule) {
        this.totalSchedule = totalSchedule;
    }

    public Long getTotalSchedule() {
        return totalSchedule;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("courseId", getCourseId())
                .append("lessonId", getLessonId())
                .append("packId", getPackId())
                .append("projectId", getProjectId())
                .append("created", getCreated())
                .append("exerciseSchedule", getExerciseSchedule())
                .append("schedule", getSchedule())
                .append("totalSchedule", getTotalSchedule())
                .toString();
    }
}
