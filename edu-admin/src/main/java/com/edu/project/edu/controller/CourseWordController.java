package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.dto.CourseWordDTO;
import com.edu.project.edu.service.ICourseWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 课程课时单词关联Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/courseWord")
@Api(tags = "课程字典单词关联API")
public class CourseWordController extends BaseController {
    @Resource
    private ICourseWordService courseWordService;

    /**
     * 删除课程课时单词关联
     */
    @PreAuthorize("@ss.hasPermi('system:word:remove')")
    @Log(title = "课程课时单词关联", businessType = BusinessType.DELETE)
    @ApiOperation("删除课程课时单词关联")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return courseWordService.deleteCourseWordByIds(ids);
    }


    /**
     * 查询课程课时单词关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:word:list')")
    @ApiOperation("查询课程课时下所有单词列表")
    @GetMapping("/getCourseWords")
    public AjaxResult getCourseWord(@RequestParam("courseId") Long courseId) {
        return courseWordService.getCourseWords(courseId);
    }


    /**
     * 查询课程课时单词关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:word:list')")
    @ApiOperation("关联课程课时下所有单词")
    @PostMapping("/setCourseWords")
    public AjaxResult setCourseWords(@RequestBody CourseWordDTO courseWordDTO) {
        return courseWordService.setCourseWords(courseWordDTO);
    }


    @PreAuthorize("@ss.hasPermi('system:word:list')")
    @Log(title = "导入课程", businessType = BusinessType.IMPORT)
    @ApiOperation("导入课程")
    @PostMapping("/importCourse")
    public AjaxResult importCourse(MultipartFile file, @RequestParam("courseId") Long courseId, @RequestParam("type") Integer type) {
        if (Objects.isNull(file) || file.isEmpty()) {
            return AjaxResult.error("Import file not exists!");
        }
        try {
            return courseWordService.importCourse(file, courseId, type);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导入异常");
        }
    }

}
