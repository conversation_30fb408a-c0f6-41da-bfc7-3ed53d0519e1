package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.WordInfo;
import com.edu.project.edu.service.IWordInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 字典信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/word")
@Api(tags = "单词字典API")
public class WordInfoController extends BaseController {
    @Resource
    private IWordInfoService wordInfoService;

    /**
     * 查询字典信息列表
     */
    @PreAuthorize("@ss.hasPermi('word:info:list')")
    @ApiOperation("查询字典信息列表")
    @GetMapping("/list")
    public TableDataInfo list(WordInfo wordInfo) {
        startPage();
        List<WordInfo> list = wordInfoService.selectWordInfoList(wordInfo);
        return getDataTable(list);
    }

    /**
     * 获取字典信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('word:info:query')")
    @ApiOperation("获取字典信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(wordInfoService.selectWordInfoById(id));
    }

    /**
     * 新增字典信息
     */
    @PreAuthorize("@ss.hasPermi('word:info:add')")
    @Log(title = "字典信息", businessType = BusinessType.INSERT)
    @ApiOperation("新增字典信息")
    @PostMapping
    public AjaxResult add(@RequestBody WordInfo wordInfo) {
        return AjaxResult.success(wordInfoService.insertWordInfo(wordInfo));
    }

    /**
     * 修改字典信息
     */
    @PreAuthorize("@ss.hasPermi('word:info:edit')")
    @Log(title = "字典信息", businessType = BusinessType.UPDATE)
    @ApiOperation("修改字典信息")
    @PutMapping
    public AjaxResult edit(@RequestBody WordInfo wordInfo) {
        return AjaxResult.success(wordInfoService.updateWordInfo(wordInfo));
    }

    /**
     * 删除字典信息
     */
    @PreAuthorize("@ss.hasPermi('word:info:remove')")
    @Log(title = "字典信息", businessType = BusinessType.DELETE)
    @ApiOperation("删除字典信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wordInfoService.deleteWordInfoByIds(ids));
    }


    @PreAuthorize("@ss.hasPermi('word:info:list')")
    @Log(title = "导入单词语音信息", businessType = BusinessType.IMPORT)
    @ApiOperation("导入单词语音信息")
    @PostMapping("/importWordAudio")
    public AjaxResult importWordAudio(MultipartFile file) {
        if (Objects.isNull(file) || file.isEmpty()) {
            return AjaxResult.error("Import file not exists!");
        }
        try {
            return wordInfoService.importWordAudio(file);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导入异常");
        }
    }

    @PreAuthorize("@ss.hasPermi('word:info:list')")
    @Log(title = "导入单词语法信息", businessType = BusinessType.IMPORT)
    @ApiOperation("导入单词语法信息")
    @PostMapping("/importPhonetic")
    public AjaxResult importPhonetic(MultipartFile file) {
        if (Objects.isNull(file) || file.isEmpty()) {
            return AjaxResult.error("Import file not exists!");
        }
        try {
            return wordInfoService.importPhonetic(file);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导入异常");
        }
    }

}
