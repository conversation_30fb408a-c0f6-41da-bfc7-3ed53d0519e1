package com.edu.project.edu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.project.edu.domain.WordTestInfo;

import java.util.List;

/**
 * 字典评测信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface WordTestInfoMapper extends BaseMapper<WordTestInfo> {
    /**
     * 查询字典评测信息
     *
     * @param id 字典评测信息ID
     * @return 字典评测信息
     */
    WordTestInfo selectWordTestInfoById(Long id);

    /**
     * 查询字典评测信息列表
     *
     * @param wordTestInfo 字典评测信息
     * @return 字典评测信息集合
     */
    List<WordTestInfo> selectWordTestInfoList(WordTestInfo wordTestInfo);

    /**
     * 新增字典评测信息
     *
     * @param wordTestInfo 字典评测信息
     * @return 结果
     */
    int insertWordTestInfo(WordTestInfo wordTestInfo);

    /**
     * 修改字典评测信息
     *
     * @param wordTestInfo 字典评测信息
     * @return 结果
     */
    int updateWordTestInfo(WordTestInfo wordTestInfo);

    /**
     * 删除字典评测信息
     *
     * @param id 字典评测信息ID
     * @return 结果
     */
    int deleteWordTestInfoById(Long id);

    /**
     * 批量删除字典评测信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWordTestInfoByIds(Long[] ids);

    List<String> selectTestKeys();

}
