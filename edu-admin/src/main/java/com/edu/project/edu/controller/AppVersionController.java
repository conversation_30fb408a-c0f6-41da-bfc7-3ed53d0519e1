package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.AppVersion;
import com.edu.project.edu.service.IAppVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用Controller
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/edu/version")
@Api(tags = "应用版本管理")
public class AppVersionController extends BaseController {
    @Autowired
    private IAppVersionService appVersionService;

    /**
     * 查询应用列表
     */
    @PreAuthorize("@ss.hasPermi('system:version:list')")
    @GetMapping("/list")
    @ApiOperation("查询应用列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "client", value = "客户端类型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "appId", value = "应用id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "分页号", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "分页数", required = true, dataType = "int")
    })
    public TableDataInfo list(String client, String appId) {
        startPage();
        List<AppVersion> list = appVersionService.selectAppVersionList(client, appId);
        return getDataTable(list);
    }


    /**
     * 新增应用
     */
    @PreAuthorize("@ss.hasPermi('system:version:add')")
    @Log(title = "应用", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增应用")
    public AjaxResult add(@RequestBody AppVersion appVersion) {
        return toAjax(appVersionService.insertAppVersion(appVersion));
    }

    /**
     * 删除应用
     */
    @PreAuthorize("@ss.hasPermi('system:version:remove')")
    @Log(title = "应用", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除应用")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(appVersionService.deleteAppVersionByIds(ids));
    }
}
