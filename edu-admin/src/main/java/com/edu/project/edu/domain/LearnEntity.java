package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 学习实体对象 learn_entity
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public class LearnEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    private Long chapterId;

    /**
     * 课程ID
     */
    @Excel(name = "课程ID")
    private Long courseId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "2024-01-01 12:12:12")
    private Date created;

    /**
     * 实体ID
     */
    @Excel(name = "实体ID")
    private Long entityId;

    /**
     * 扩展信息
     */
    @Excel(name = "扩展信息")
    private String ext;

    /**
     * 是否为综合练习
     */
    @Excel(name = "是否为综合练习")
    private Integer isCompExercise;

    /**
     * 是否错误
     */
    @Excel(name = "是否错误")
    private Integer isError;

    /**
     * 是否来自考试
     */
    @Excel(name = "是否来自考试")
    private Integer isFromExam;

    /**
     * 是否来自跟踪
     */
    @Excel(name = "是否来自跟踪")
    private Integer isFromTrack;

    /**
     * 是否仅保存点
     */
    @Excel(name = "是否仅保存点")
    private Integer isOnlySavePoint;

    /**
     * 上次是否错误
     */
    @Excel(name = "上次是否错误")
    private Integer lastIsError;

    /**
     * 上次复习日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上次复习日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastReviewDate;

    /**
     * 课时ID
     */
    @Excel(name = "课时ID")
    private Long lessonId;

    /**
     * 包ID
     */
    @Excel(name = "包ID")
    private Long packId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 复习次数
     */
    @Excel(name = "复习次数")
    private Long reviewNum;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tag;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setChapterId(Long chapterId) {
        this.chapterId = chapterId;
    }

    public Long getChapterId() {
        return chapterId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getCreated() {
        return created;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getExt() {
        return ext;
    }

    public void setIsCompExercise(Integer isCompExercise) {
        this.isCompExercise = isCompExercise;
    }

    public Integer getIsCompExercise() {
        return isCompExercise;
    }

    public void setIsError(Integer isError) {
        this.isError = isError;
    }

    public Integer getIsError() {
        return isError;
    }

    public void setIsFromExam(Integer isFromExam) {
        this.isFromExam = isFromExam;
    }

    public Integer getIsFromExam() {
        return isFromExam;
    }

    public void setIsFromTrack(Integer isFromTrack) {
        this.isFromTrack = isFromTrack;
    }

    public Integer getIsFromTrack() {
        return isFromTrack;
    }

    public void setIsOnlySavePoint(Integer isOnlySavePoint) {
        this.isOnlySavePoint = isOnlySavePoint;
    }

    public Integer getIsOnlySavePoint() {
        return isOnlySavePoint;
    }

    public void setLastIsError(Integer lastIsError) {
        this.lastIsError = lastIsError;
    }

    public Integer getLastIsError() {
        return lastIsError;
    }

    public void setLastReviewDate(Date lastReviewDate) {
        this.lastReviewDate = lastReviewDate;
    }

    public Date getLastReviewDate() {
        return lastReviewDate;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    public Long getPackId() {
        return packId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setReviewNum(Long reviewNum) {
        this.reviewNum = reviewNum;
    }

    public Long getReviewNum() {
        return reviewNum;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTag() {
        return tag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("chapterId", getChapterId())
                .append("courseId", getCourseId())
                .append("created", getCreated())
                .append("entityId", getEntityId())
                .append("ext", getExt())
                .append("isCompExercise", getIsCompExercise())
                .append("isError", getIsError())
                .append("isFromExam", getIsFromExam())
                .append("isFromTrack", getIsFromTrack())
                .append("isOnlySavePoint", getIsOnlySavePoint())
                .append("lastIsError", getLastIsError())
                .append("lastReviewDate", getLastReviewDate())
                .append("lessonId", getLessonId())
                .append("packId", getPackId())
                .append("projectId", getProjectId())
                .append("reviewNum", getReviewNum())
                .append("tag", getTag())
                .toString();
    }
}
