package com.edu.project.edu.domain.dto;

import com.edu.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhoneticImportDTO implements Serializable {

    @ApiModelProperty("单词ID")
    @Excel(name = "word_id")
    private Long wordId;

    @ApiModelProperty("英式发音解释")
    @Excel(name = "phonetic_uk")
    private String phoneticUk;

    @ApiModelProperty("美式发音解释")
    @Excel(name = "phonetic_us")
    private String phoneticUs;

}
