package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 字典信息对象 word_info
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class WordInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("词汇")
    @Excel(name = "词汇")
    private String word;

    @ApiModelProperty("类别(1、单词 2、短语 3、句子)")
    @Excel(name = "类别(1、单词 2、短语 3、句子)")
    private Integer type;

    @ApiModelProperty("翻译")
    @Excel(name = "翻译")
    private String basicExplain;

    @ApiModelProperty("其他翻译")
    @Excel(name = "其他翻译")
    private String allExplain;

    @ApiModelProperty("样例")
    @Excel(name = "样例")
    private String example;

    @ApiModelProperty("语法")
    @Excel(name = "语法")
    private String reference;

    @ApiModelProperty("单词短语")
    @Excel(name = "单词短语")
    private String phrase;

    @ApiModelProperty("语音读法(英式)")
    @Excel(name = "语音读法(英式)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String phoneticUk;

    @ApiModelProperty("语音读法(美式)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "语音读法(美式)")
    private String phoneticUs;

    @ApiModelProperty("图片地址")
    @Excel(name = "图片地址")
    private String photoUrl;

    @ApiModelProperty("音频(英式)")
    @Excel(name = "音频(英式)")
    private String audioUk;

    @ApiModelProperty("音频(美式)")
    @Excel(name = "音频(美式)")
    private String audioUs;

    @ApiModelProperty("音频文件名称(远程文件)")
    @Excel(name = "音频文件名称(远程文件)")
    private String audio;

    @ApiModelProperty("图片名称(远程文件)")
    @Excel(name = "图片名称(远程文件)")
    private String photo;

    @ApiModelProperty("难题")
    @Excel(name = "难题")
    private String difficulty;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("版本")
    @Excel(name = "版本")
    private Integer version;

    @ApiModelProperty("状态是否启用(0.上线 1.下线)")
    @Excel(name = "状态是否启用(0.上线 1.下线)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

    @ApiModelProperty("单词考试记录")
    @TableField(exist = false)
    private List<WordTestInfo> wordTestInfoList;
}
