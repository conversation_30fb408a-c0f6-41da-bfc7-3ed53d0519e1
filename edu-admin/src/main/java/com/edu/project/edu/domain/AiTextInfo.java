package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * AI数字人课文信息对象 ai_text_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class AiTextInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 上级ID（单元才有）
     */
    private Long topId;

    /**
     * 教育阶段ID
     */
    @Excel(name = "教育阶段ID")
    private Long stageId;

    /**
     * 教育阶段名称
     */
    @Excel(name = "教育阶段名称")
    private String stageName;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private Integer type;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 封面
     */
    @Excel(name = "封面")
    private String cover;

    /**
     * 介绍简介
     */
    @Excel(name = "介绍简介")
    private String description;

    /**
     * 排序值
     */
    @Excel(name = "排序值")
    private Integer sort;

    /**
     * 其他信息
     */
    @Excel(name = "其他信息")
    private String otherInfo;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * tenant_id
     */
    @Excel(name = "tenant_id")
    private Long tenantId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

}
