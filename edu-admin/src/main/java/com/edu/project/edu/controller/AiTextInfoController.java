package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.AiTextInfo;
import com.edu.project.edu.service.IAiTextInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI数字人课文信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/ai/text")
@Api(tags = "AI数字人课文信息API")
public class AiTextInfoController extends BaseController {
//    @Autowired
//    private IAiTextInfoService aiTextInfoService;
//
//    /**
//     * 查询AI数字人课文信息列表
//     */
//    @ApiOperation("查询AI数字人课文信息列表")
//    @GetMapping("/list")
//    public TableDataInfo list(AiTextInfo aiTextInfo) {
//        startPage();
//        List<AiTextInfo> list = aiTextInfoService.selectAiTextInfoList(aiTextInfo);
//        return getDataTable(list);
//    }
//
//
//    /**
//     * 获取AI数字人课文信息详细信息
//     */
//    @ApiOperation("获取AI数字人课文信息详细信息")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return AjaxResult.success(aiTextInfoService.selectAiTextInfoById(id));
//    }
//
//    /**
//     * 新增AI数字人课文信息
//     */
//    @ApiOperation("新增AI数字人课文信息")
//    @Log(title = "AI数字人课文信息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody AiTextInfo aiTextInfo) {
//        return toAjax(aiTextInfoService.insertAiTextInfo(aiTextInfo));
//    }
//
//    /**
//     * 修改AI数字人课文信息
//     */
//    @ApiOperation("修改AI数字人课文信息")
//    @Log(title = "AI数字人课文信息", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody AiTextInfo aiTextInfo) {
//        return toAjax(aiTextInfoService.updateAiTextInfo(aiTextInfo));
//    }
//
//    /**
//     * 删除AI数字人课文信息
//     */
//    @ApiOperation("删除AI数字人课文信息")
//    @Log(title = "AI数字人课文信息", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(aiTextInfoService.deleteAiTextInfoByIds(ids));
//    }
}
