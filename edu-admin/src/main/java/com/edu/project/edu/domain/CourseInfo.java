package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 课程信息对象 course_info
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CourseInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("课程包ID")
    @Excel(name = "课程包ID")
    private Long coursePackId;

    @ApiModelProperty("上级ID(顶级[课程]为null)")
    @Excel(name = "上级ID(顶级[课程]为null)")
    private Long topId;

    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;

    @ApiModelProperty("课程类别(1、认读 2、拼写 3、辨音)")
    @Excel(name = "课程类别(1、认读 2、拼写 3、辨音)")
    private Integer courseType;

    @ApiModelProperty("层级(1、课程 2、章节 3、课时)")
    @Excel(name = "层级(1、课程 2、章节 3、课时)")
    private Integer level;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("状态是否启用(0.上线 1.下线)")
    @Excel(name = "状态是否启用(0.上线 1.下线)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

    @ApiModelProperty("课程子集")
    @TableField(exist = false)
    private List<CourseInfo> children;

    @ApiModelProperty(value = "下载地址")
    private String downloadUrl;
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;
    @ApiModelProperty(value = "校验和")
    private String checksum;
    @ApiModelProperty(value = "版本")
    private Long version;
}
