package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程课时单词关联对象 course_word
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class CourseWord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("课程包ID")
    @Excel(name = "课程包ID")
    private Long coursePackId;

    @ApiModelProperty("课时ID")
    @Excel(name = "课时ID")
    private Long courseId;

    @ApiModelProperty("单词字典ID")
    @Excel(name = "单词字典ID")
    private Long wordId;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("状态是否启用(0.上线 1.下线)")
    @Excel(name = "状态是否启用(0.上线 1.下线)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

    @ApiModelProperty("单词信息")
    @TableField(exist = false)
    private WordInfo wordInfo;
}
