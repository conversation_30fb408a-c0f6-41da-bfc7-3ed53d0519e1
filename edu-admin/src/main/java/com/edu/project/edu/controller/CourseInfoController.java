package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.CourseInfo;
import com.edu.project.edu.service.ICourseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 课程信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/course")
@Api(tags = "课程API")
public class CourseInfoController extends BaseController {
    @Resource
    private ICourseInfoService courseInfoService;

    /**
     * 查询课程信息列表
     */
    @PreAuthorize("@ss.hasPermi('course:info:list')")
    @GetMapping("/list")
    @ApiOperation("查询课程信息列表(树状结构)")
    public AjaxResult list(CourseInfo courseInfo) {
        return AjaxResult.success(courseInfoService.selectCourseInfoList(courseInfo));
    }

    /**
     * 查询课程包下面课程信息列表(非树状结构)
     */
    @PreAuthorize("@ss.hasPermi('course:info:list')")
    @GetMapping("/getCourseList")
    @ApiOperation("查询课程包下面课程信息列表(非树状结构)")
    public AjaxResult getCourseList(@RequestParam("coursePackId") @ApiParam("课程包ID") Long coursePackId,
                                    @RequestParam(value = "courseType", required = false) @ApiParam("课程类别(1、认读 2、拼写 3、辨音)") Integer courseType) {
        return courseInfoService.getCourseList(coursePackId, courseType);
    }

    /**
     * 查询课程课时下级信息列表(非树状结构)
     */
//    @PreAuthorize("@ss.hasPermi('course:info:list')")
    @GetMapping("/getNextList")
    @ApiOperation("查询课程课时下级信息列表(非树状结构)")
    public AjaxResult getNextList(@RequestParam("topId") @ApiParam("上级ID") Long topId) {
        return courseInfoService.getNextList(topId);
    }

    /**
     * 获取课程信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('course:info:query')")
    @ApiOperation("获取课程信息详细信息(树状结构)")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(courseInfoService.selectCourseInfoById(id));
    }

    /**
     * 新增课程信息
     */
    @PreAuthorize("@ss.hasPermi('course:info:add')")
    @Log(title = "课程信息", businessType = BusinessType.INSERT)
    @ApiOperation("新增课程信息")
    @PostMapping
    public AjaxResult add(@RequestBody CourseInfo courseInfo) {
        return AjaxResult.success(courseInfoService.insertCourseInfo(courseInfo));
    }

    /**
     * 修改课程信息
     */
    @PreAuthorize("@ss.hasPermi('course:info:edit')")
    @Log(title = "课程信息", businessType = BusinessType.UPDATE)
    @ApiOperation("修改课程信息")
    @PutMapping
    public AjaxResult edit(@RequestBody CourseInfo courseInfo) {
        return AjaxResult.success(courseInfoService.updateCourseInfo(courseInfo));
    }

    /**
     * 删除课程信息
     */
    @PreAuthorize("@ss.hasPermi('course:info:remove')")
    @Log(title = "课程信息", businessType = BusinessType.DELETE)
    @ApiOperation("删除课程信息(单条)")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return courseInfoService.deleteCourseInfoById(id);
    }


    @GetMapping("/nextList")
    @ApiOperation("查询下级数据列表(传课程id、章节id)")
    public AjaxResult nextList(@RequestParam("id") Long id) {
        return AjaxResult.success(courseInfoService.nextList(id));
    }

}
