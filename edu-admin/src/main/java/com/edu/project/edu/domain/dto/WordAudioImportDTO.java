package com.edu.project.edu.domain.dto;

import com.edu.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WordAudioImportDTO implements Serializable {
    @ApiModelProperty("ID")
    @Excel(name = "id")
    private Long id;

    @ApiModelProperty("单词ID")
    @Excel(name = "word_id")
    private Long wordId;

    @ApiModelProperty("单词")
    @Excel(name = "word")
    private String word;

    @ApiModelProperty("图片")
    @Excel(name = "photo")
    private String photo;

    @ApiModelProperty("美式发音")
    @Excel(name = "audio_us")
    private String audioUs;

    @ApiModelProperty("英式发音")
    @Excel(name = "audio_uk")
    private String audioUk;

    @ApiModelProperty("版本")
    @Excel(name = "version")
    private Integer version;
}
