package com.edu.project.edu.common.task;

import com.edu.common.task.Task;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class TestTask extends Task {
    private final Log logger = LogFactory.getLog(TestTask.class);

    public TestTask(long delayInMilliseconds) {
        super("", delayInMilliseconds);
    }

    @Override
    public void run() {
        logger.info("系统开始处理延时任务---测试---");


        logger.info("系统结束处理延时任务---测试---");
    }
}
