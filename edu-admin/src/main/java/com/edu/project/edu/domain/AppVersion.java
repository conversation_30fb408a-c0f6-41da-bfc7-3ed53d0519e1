package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 应用更新对象 app_version
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "应用更新对象", description = "应用更新信息")
public class AppVersion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", hidden = true)
    private Long id;

    /**
     * 应用ID
     */
    @Excel(name = "应用ID")
    @ApiModelProperty(value = "应用ID", required = true, example = "1")
    private String appId;

    /**
     * 下载地址
     */
    @Excel(name = "下载地址")
    @ApiModelProperty(value = "下载地址", required = true)
    private String downloadUrl;

    /**
     * 下载地址
     */
    @Excel(name = "配置文件")
    @ApiModelProperty(value = "配置文件地址", required = true)
    private String configUrl;

    /**
     * 文件大小
     */
    @Excel(name = "文件大小")
    @ApiModelProperty(value = "文件大小", required = true)
    private Long fileSize;

    /**
     * SHA校验值
     */
    @Excel(name = "SHA校验值")
    @ApiModelProperty(value = "SHA校验值", required = true)
    private String sha;

    /**
     * 更新摘要
     */
    @Excel(name = "更新摘要")
    @ApiModelProperty(value = "更新摘要", required = true)
    @NotBlank
    private String summary;

    /**
     * 版本号
     */
    @Excel(name = "客户端类型：ios/android/pc")
    @ApiModelProperty(value = "客户端类型：ios/android/pc", required = true, example = "android")
    private String client;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long versionNo;

    /**
     * 版本字符串
     */
    @Excel(name = "版本字符串")
    @ApiModelProperty(value = "版本字符串", required = true)
    private String versionStr;

}
