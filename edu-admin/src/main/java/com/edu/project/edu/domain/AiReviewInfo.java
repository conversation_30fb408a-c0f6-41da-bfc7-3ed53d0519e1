package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * AI数字人复习单词信息对象 ai_review_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class AiReviewInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 课程包ID
     */
    @Excel(name = "课程包ID")
    private Long coursePackId;


    @Excel(name = "教育阶段ID(1、小学 2、初中 3、高中)")
    private Long eduStage;


    /**
     * 单词ID
     */
    @Excel(name = "单词ID")
    private Long wordId;

    /**
     * 单词
     */
    @Excel(name = "单词")
    private String word;

    /**
     * 单词错误时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "单词错误时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date mistakeTime;

    /**
     * 复习次数
     */
    @Excel(name = "复习次数")
    private Integer reviewNum;

    /**
     * 状态(0.未复习 1.已复习)
     */
    @Excel(name = "状态(0.未复习 1.已复习)")
    private Integer status;

    /**
     * tenant_id
     */
    @Excel(name = "tenant_id")
    private Long tenantId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

}
