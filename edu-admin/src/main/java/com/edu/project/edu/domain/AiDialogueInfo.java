package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * AI数字人课文语句对象 ai_dialogue_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class AiDialogueInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 单元ID
     */
    @Excel(name = "单元ID")
    private Long textId;

    /**
     * 课文语句
     */
    @Excel(name = "中文语句")
    private String lesson;

    /**
     * 英文语句
     */
    @Excel(name = "英文语句")
    private String enText;

    /**
     * 对话顺序
     */
    @Excel(name = "对话顺序")
    private Integer talkOrder;

    /**
     * 例句解析与例句
     */
    @Excel(name = "例句解析与例句")
    private String examAnalysis;


    /**
     * 重点句子结构分析
     */
    @Excel(name = "重点句子结构分析")
    private String otherInfo;

    /**
     * 状态
     */
    @Excel(name = "状态(1.已学习)")
    private Integer status;

    /**
     * tenant_id
     */
    @Excel(name = "tenant_id")
    private Long tenantId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

}
