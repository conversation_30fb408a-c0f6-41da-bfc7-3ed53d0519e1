package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 学习考试信息对象 learn_exam
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public class LearnExam extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 变更类型
     */
    @Excel(name = "变更类型")
    private Long changeType;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    private Long chapterId;

    /**
     * 正确数量
     */
    @Excel(name = "正确数量")
    private Long correctNum;

    /**
     * 课程ID
     */
    @Excel(name = "课程ID")
    private Long courseId;

    /**
     * 覆盖率
     */
    @Excel(name = "覆盖率")
    private Long coverRate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "2024-01-01 12:12:12")
    private Date created;

    /**
     * 持续时间（秒）
     */
    @Excel(name = "持续时间", readConverterExp = "秒=")
    private Long duration;

    /**
     * 错误数量
     */
    @Excel(name = "错误数量")
    private Long errorNum;

    /**
     * 扩展信息
     */
    @Excel(name = "扩展信息")
    private String ext;

    /**
     * 是否完成
     */
    @Excel(name = "是否完成")
    private Integer isFinished;

    /**
     * 课时ID
     */
    @Excel(name = "课时ID")
    private Long lessonId;

    /**
     * 包ID
     */
    @Excel(name = "包ID")
    private Long packId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 考试记录
     */
    @Excel(name = "考试记录")
    private String record;

    /**
     * 分数
     */
    @Excel(name = "分数")
    private Long score;

    /**
     * 阶段
     */
    @Excel(name = "阶段")
    private String stage;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tag;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 总数量
     */
    @Excel(name = "总数量")
    private Long totalNum;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private Long type;

    /**
     * 类型ID
     */
    @Excel(name = "类型ID")
    private Long typeId;

    /**
     * 未回答数量
     */
    @Excel(name = "未回答数量")
    private Long unanswerNum;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setChangeType(Long changeType) {
        this.changeType = changeType;
    }

    public Long getChangeType() {
        return changeType;
    }

    public void setChapterId(Long chapterId) {
        this.chapterId = chapterId;
    }

    public Long getChapterId() {
        return chapterId;
    }

    public void setCorrectNum(Long correctNum) {
        this.correctNum = correctNum;
    }

    public Long getCorrectNum() {
        return correctNum;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCoverRate(Long coverRate) {
        this.coverRate = coverRate;
    }

    public Long getCoverRate() {
        return coverRate;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getCreated() {
        return created;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getDuration() {
        return duration;
    }

    public void setErrorNum(Long errorNum) {
        this.errorNum = errorNum;
    }

    public Long getErrorNum() {
        return errorNum;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getExt() {
        return ext;
    }

    public void setIsFinished(Integer isFinished) {
        this.isFinished = isFinished;
    }

    public Integer getIsFinished() {
        return isFinished;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    public Long getPackId() {
        return packId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setRecord(String record) {
        this.record = record;
    }

    public String getRecord() {
        return record;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Long getScore() {
        return score;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getStage() {
        return stage;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTag() {
        return tag;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public Long getType() {
        return type;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setUnanswerNum(Long unanswerNum) {
        this.unanswerNum = unanswerNum;
    }

    public Long getUnanswerNum() {
        return unanswerNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("changeType", getChangeType())
                .append("chapterId", getChapterId())
                .append("correctNum", getCorrectNum())
                .append("courseId", getCourseId())
                .append("coverRate", getCoverRate())
                .append("created", getCreated())
                .append("duration", getDuration())
                .append("errorNum", getErrorNum())
                .append("ext", getExt())
                .append("isFinished", getIsFinished())
                .append("lessonId", getLessonId())
                .append("packId", getPackId())
                .append("projectId", getProjectId())
                .append("record", getRecord())
                .append("score", getScore())
                .append("stage", getStage())
                .append("tag", getTag())
                .append("title", getTitle())
                .append("totalNum", getTotalNum())
                .append("type", getType())
                .append("typeId", getTypeId())
                .append("unanswerNum", getUnanswerNum())
                .toString();
    }
}
