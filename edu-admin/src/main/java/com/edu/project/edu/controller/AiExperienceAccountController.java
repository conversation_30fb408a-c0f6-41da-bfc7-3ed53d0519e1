package com.edu.project.edu.controller;

import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.project.edu.service.IAiExperienceAccountService;
import com.edu.system.domain.vo.SysExperienceAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI体验帐号Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/ai/experience")
@Api(tags = "AI体验帐号API")
public class AiExperienceAccountController extends BaseController {

    @Autowired
    private IAiExperienceAccountService aiExperienceAccountService;

    /**
     * 删除体验帐号数据
     */
    @PreAuthorize("@ss.hasPermi('system:experience:account:remove')")
    @ApiOperation("删除体验帐号数据")
    @PostMapping(value = "/{ids}")
    public AjaxResult deleteIdsMessage(@PathVariable("ids") List<Long> ids) {
        return AjaxResult.success(aiExperienceAccountService.deleteAiExperienceAccountId(ids));
    }

    /**
     * 查询体验帐号列表
     */
    @PreAuthorize("@ss.hasPermi('system:experience:account:list')")
    @ApiOperation("查询体验帐号列表")
    @GetMapping("/list")
    public TableDataInfo list() {
        startPage();
        List<SysExperienceAccountVO> list = aiExperienceAccountService.selectAiExperienceAccountList();
        return getDataTable(list);
    }

}
