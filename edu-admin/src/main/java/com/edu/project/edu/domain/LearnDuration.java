package com.edu.project.edu.domain;

import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 学习时长记录对象 learn_duration
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public class LearnDuration extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 分类ID
     */
    @Excel(name = "分类ID")
    private Long categoryId;

    /**
     * 课程ID
     */
    @Excel(name = "课程ID")
    private Long courseId;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date created;

    /**
     * 学习时长
     */
    @Excel(name = "学习时长")
    private Long duration;

    /**
     * 是否复习
     */
    @Excel(name = "是否复习")
    private Integer isReview;

    /**
     * 包ID
     */
    @Excel(name = "包ID")
    private Long packId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tag;

    /**
     * 时间框架
     */
    @Excel(name = "时间框架")
    private Long timeFrame;

    /**
     * 总时长
     */
    @Excel(name = "总时长")
    private Long totalDuration;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getCreated() {
        return created;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getDuration() {
        return duration;
    }

    public void setIsReview(Integer isReview) {
        this.isReview = isReview;
    }

    public Integer getIsReview() {
        return isReview;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    public Long getPackId() {
        return packId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTag() {
        return tag;
    }

    public void setTimeFrame(Long timeFrame) {
        this.timeFrame = timeFrame;
    }

    public Long getTimeFrame() {
        return timeFrame;
    }

    public void setTotalDuration(Long totalDuration) {
        this.totalDuration = totalDuration;
    }

    public Long getTotalDuration() {
        return totalDuration;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("categoryId", getCategoryId())
                .append("courseId", getCourseId())
                .append("userId", getUserId())
                .append("created", getCreated())
                .append("duration", getDuration())
                .append("isReview", getIsReview())
                .append("packId", getPackId())
                .append("projectId", getProjectId())
                .append("tag", getTag())
                .append("timeFrame", getTimeFrame())
                .append("totalDuration", getTotalDuration())
                .toString();
    }
}
