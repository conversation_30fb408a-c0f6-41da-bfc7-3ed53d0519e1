package com.edu.project.edu.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.edu.common.annotation.Excel;
import com.edu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字典评测信息对象 word_test_info
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class WordTestInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("单词ID")
    @Excel(name = "单词ID")
    private Long wordId;

    @ApiModelProperty("课程ID")
    @Excel(name = "课程ID")
    private Long courseId;

    @ApiModelProperty("评测类别（1、英译汉 2、汉译英）")
    @Excel(name = "评测类别", readConverterExp = "1=、英译汉,2=、汉译英")
    private Integer type;

    @ApiModelProperty("正确值")
    @Excel(name = "正确值")
    private String correct;

    @ApiModelProperty("错误1")
    @Excel(name = "错误1")
    private String error1;

    @ApiModelProperty("错误2")
    @Excel(name = "错误2")
    private String error2;

    @ApiModelProperty("错误3")
    @Excel(name = "错误3")
    private String error3;

    @ApiModelProperty("排序值")
    @Excel(name = "排序值")
    private Integer sort;

    @ApiModelProperty("导数唯一编码(wordId + type + type值)")
    @Excel(name = "导数唯一编码(wordId + type + type值)")
    private String testKey;

    @ApiModelProperty("状态是否启用(0.上线 1.下线)")
    @Excel(name = "状态是否启用(0.上线 1.下线)")
    private Integer status;

    @ApiModelProperty("tenant_id")
    @Excel(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer deleted;

    @ApiModelProperty("单词")
    @TableField(exist = false)
    private String word;
}
