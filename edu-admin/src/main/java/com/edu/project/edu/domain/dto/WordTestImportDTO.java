package com.edu.project.edu.domain.dto;

import com.edu.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WordTestImportDTO implements Serializable {

    @ApiModelProperty("ID")
    @Excel(name = "id")
    private Long id;

    @ApiModelProperty("单词ID")
    @Excel(name = "word_id")
    private Long wordId;

    @ApiModelProperty("单词")
    @Excel(name = "word")
    private String word;

    @ApiModelProperty("正确值")
    @Excel(name = "correct")
    private String correct;

    @ApiModelProperty("错误1")
    @Excel(name = "error1")
    private String error1;

    @ApiModelProperty("错误2")
    @Excel(name = "error2")
    private String error2;

    @ApiModelProperty("错误3")
    @Excel(name = "error3")
    private String error3;

    @ApiModelProperty("类型")
    @Excel(name = "type")
    private Integer type;

}
