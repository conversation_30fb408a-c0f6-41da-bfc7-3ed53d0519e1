package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.CoursePackInfo;
import com.edu.project.edu.service.ICoursePackInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 课程包信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/edu/coursePack")
@Api(tags = "课程包API")
public class CoursePackInfoController extends BaseController {
    @Resource
    private ICoursePackInfoService coursePackInfoService;

    /**
     * 查询课程包信息列表
     */
//    @PreAuthorize("@ss.hasPermi('coursePack:info:list')")
    @ApiOperation("查询课程包信息列表（分页）")
    @GetMapping("/list")
    public TableDataInfo list(CoursePackInfo coursePackInfo) {
        startPage();
        List<CoursePackInfo> list = coursePackInfoService.selectCoursePackInfoList(coursePackInfo);
        return getDataTable(list);
    }


    /**
     * 获取课程包信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('coursePack:info:query')")
    @ApiOperation("获取课程包信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(coursePackInfoService.selectCoursePackInfoById(id));
    }


    @PreAuthorize("@ss.hasPermi('coursePack:info:query')")
    @ApiOperation("上架课程包")
    @GetMapping(value = "/putAway")
    public AjaxResult putAway(@RequestParam("id") Long id) {
        return coursePackInfoService.putAway(id);
    }

    @PreAuthorize("@ss.hasPermi('coursePack:info:query')")
    @ApiOperation("下架课程包")
    @GetMapping(value = "/soldOut")
    public AjaxResult soldOut(@RequestParam("id") Long id) {
        return coursePackInfoService.soldOut(id);
    }

    @PreAuthorize("@ss.hasPermi('coursePack:info:query')")
    @ApiOperation("发布课程包")
    @GetMapping(value = "/publish")
    public AjaxResult publish(@RequestParam("id") Long id) {
        return coursePackInfoService.publish(id);
    }

    @PreAuthorize("@ss.hasPermi('coursePack:info:query')")
    @ApiOperation("作废课程包")
    @GetMapping(value = "/cancel")
    public AjaxResult cancel(@RequestParam("id") Long id) {
        return coursePackInfoService.cancel(id);
    }


    /**
     * 新增课程包信息
     */
    @PreAuthorize("@ss.hasPermi('coursePack:info:add')")
    @Log(title = "课程包信息", businessType = BusinessType.INSERT)
    @ApiOperation("新增课程包信息")
    @PostMapping
    public AjaxResult add(@RequestBody CoursePackInfo coursePackInfo) {
        return AjaxResult.success(coursePackInfoService.insertCoursePackInfo(coursePackInfo));
    }

    /**
     * 修改课程包信息
     */
    @PreAuthorize("@ss.hasPermi('coursePack:info:edit')")
    @Log(title = "课程包信息", businessType = BusinessType.UPDATE)
    @ApiOperation("修改课程包信息")
    @PutMapping
    public AjaxResult edit(@RequestBody CoursePackInfo coursePackInfo) {
        return AjaxResult.success(coursePackInfoService.updateCoursePackInfo(coursePackInfo));
    }

    /**
     * 删除课程包信息
     */
    @PreAuthorize("@ss.hasPermi('coursePack:info:remove')")
    @Log(title = "课程包信息", businessType = BusinessType.DELETE)
    @ApiOperation("删除课程包信息")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return coursePackInfoService.deleteCoursePackInfoById(id);
    }
}
