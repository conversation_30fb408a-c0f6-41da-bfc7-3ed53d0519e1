package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.AiTimeCount;
import com.edu.project.edu.service.IAiTimeCountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI数字人时长统计Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/ai/timeCount")
@Api(tags = "AI数字人时长统计API")
public class AiTimeCountController extends BaseController {
    @Autowired
    private IAiTimeCountService aiTimeCountService;

    /**
     * 查询AI数字人时长统计列表
     */
    @ApiOperation("查询AI数字人时长统计列表")
    @GetMapping("/list")
    public TableDataInfo list(AiTimeCount aiTimeCount) {
        startPage();
        List<AiTimeCount> list = aiTimeCountService.selectAiTimeCountList(aiTimeCount);
        return getDataTable(list);
    }

    /**
     * 获取AI数字人时长统计详细信息
     */
    @ApiOperation("获取AI数字人时长统计详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(aiTimeCountService.selectAiTimeCountById(id));
    }

    /**
     * 新增AI数字人时长统计
     */
    @ApiOperation("新增AI数字人时长统计")
    @Log(title = "AI数字人时长统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AiTimeCount aiTimeCount) {
        return toAjax(aiTimeCountService.insertAiTimeCount(aiTimeCount));
    }

    /**
     * 修改AI数字人时长统计
     */
    @ApiOperation("修改AI数字人时长统计")
    @Log(title = "AI数字人时长统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AiTimeCount aiTimeCount) {
        return toAjax(aiTimeCountService.updateAiTimeCount(aiTimeCount));
    }

    /**
     * 删除AI数字人时长统计
     */
    @ApiOperation("删除AI数字人时长统计")
    @Log(title = "AI数字人时长统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(aiTimeCountService.deleteAiTimeCountByIds(ids));
    }
}
