package com.edu.project.edu.controller;

import com.edu.common.annotation.Log;
import com.edu.common.core.controller.BaseController;
import com.edu.common.core.domain.AjaxResult;
import com.edu.common.core.page.TableDataInfo;
import com.edu.common.enums.BusinessType;
import com.edu.project.edu.domain.AiReviewInfo;
import com.edu.project.edu.service.IAiReviewInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI数字人复习单词信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/ai/review")
@Api(tags = "AI数字人复习单词信息API")
public class AiReviewInfoController extends BaseController {
    @Autowired
    private IAiReviewInfoService aiReviewInfoService;

}
