package com.edu.system.domain.vo;

import com.edu.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class SysExperienceAccountVO extends BaseEntity {
    private Long userId;          // 用户ID
    private Long deptId;          // 部门ID
    private String userName;      // 用户账号
    private String nickName;      // 用户昵称
    private String userType;      // 用户类型（00-APP用户，...）
    private String email;         // 用户邮箱
    private String phonenumber;   // 手机号码
    private String sex;           // 用户性别（0男 1女 2未知）
    private String avatar;        // 头像地址
    private String password;      // 密码
    private String status;        // 账号状态（0正常 1停用）
    private String delFlag;       // 删除标志（0代表存在 2代表删除）
//    private String loginIp;       // 最后登录IP
//    private Date loginDate;       // 最后登录时间
//    private String createBy;      // 创建者
//    private Date createTime;      // 创建时间
//    private String updateBy;      // 更新者
//    private Date updateTime;      // 更新时间
//    private String remark;        // 备注
    private Boolean shouldModify; // 是否应修改密码
    private Long stageId;         // 阶段ID
    private Long gradeId;         // 年级ID
    private Long country;         // 国家
    private Long province;        // 省
    private Long city;            // 市
    private String birthday;      // 生日
    private Long groupId;         // 用户分组ID（默认0）
}
